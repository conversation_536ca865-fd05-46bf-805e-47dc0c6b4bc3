{"display_name": "多存储文件存储服务", "description": "支持多种存储方式的文件存储MCP工具，包括本地存储、阿里云OSS、MinIO等", "version": "2.0.0", "author": "Coli AI", "storage_types": {"local": {"name": "本地存储", "description": "本地文件系统存储", "optional_env": ["LOCAL_STORAGE_BASE_DIR", "LOCAL_STORAGE_CREATE_SUBDIRS", "LOCAL_STORAGE_MAX_FILE_SIZE", "LOCAL_STORAGE_ALLOWED_EXTENSIONS"]}, "oss": {"name": "阿里云OSS", "description": "阿里云对象存储服务", "optional_env": ["OSS_ACCESS_KEY_ID", "OSS_ACCESS_KEY_SECRET", "OSS_ENDPOINT", "OSS_BUCKET_NAME"], "default_config": "内置默认SSO配置，开箱即用"}, "minio": {"name": "MinIO存储", "description": "MinIO对象存储服务", "optional_env": ["MINIO_ENDPOINT", "MINIO_ACCESS_KEY", "MINIO_SECRET_KEY", "MINIO_BUCKET_NAME", "MINIO_SECURE"], "requires_package": "minio"}}, "features": ["多存储方式支持（本地/OSS/MinIO）", "统一API接口", "Base64内容上传", "本地文件上传", "文件列表管理", "文件删除功能", "访问URL生成", "存储连接测试", "自动文件类型检测", "时间戳命名防重复", "灵活配置管理", "易于扩展新存储方式"], "tools": [{"name": "upload_file_content", "description": "上传Base64编码的文件内容到指定存储", "parameters": ["file_content_base64", "filename", "storage_type", "**config"]}, {"name": "upload_local_file", "description": "上传本地文件到指定存储", "parameters": ["file_path", "object_name", "storage_type", "**config"]}, {"name": "list_files", "description": "列出指定存储中的文件", "parameters": ["prefix", "storage_type", "**config"]}, {"name": "delete_file", "description": "删除指定存储中的文件", "parameters": ["object_name", "storage_type", "**config"]}, {"name": "get_file_url", "description": "获取指定存储中文件的访问URL", "parameters": ["object_name", "expires_in", "storage_type", "**config"]}, {"name": "test_storage_connection", "description": "测试指定存储的连接配置", "parameters": ["storage_type", "**config"]}, {"name": "get_storage_info", "description": "获取文件存储工具的信息和使用说明", "parameters": []}], "resources": [{"uri": "file-storage://info", "description": "获取存储信息资源"}, {"uri": "file-storage://config-template", "description": "获取配置模板资源"}], "config_priority": ["函数参数配置（最高优先级）", "环境变量配置", "配置文件", "默认配置（最低优先级）"], "architecture": {"pattern": "抽象工厂模式 + 策略模式", "extensible": true, "description": "易于扩展新的存储方式，只需实现BaseStorageProvider接口"}, "endpoints": {"http": "/file-storage/mcp", "sse": "/file-storage-sse/sse"}, "files": {"main_service": "server_new.py", "legacy_service": "server.py", "test_script": "test_storage.py", "example_script": "example_usage.py", "documentation": ["README_NEW.md", "REFACTOR_SUMMARY.md"]}, "usage_examples": {"local_storage": {"upload": "await upload_file_content(file_content_base64='...', filename='test.txt', storage_type='local')", "list": "await list_files(storage_type='local')", "delete": "await delete_file(object_name='test.txt', storage_type='local')"}, "oss_storage": {"upload": "await upload_file_content(file_content_base64='...', filename='test.txt', storage_type='oss')", "with_config": "await upload_file_content(file_content_base64='...', filename='test.txt', storage_type='oss', access_key_id='xxx', bucket_name='yyy')"}, "minio_storage": {"upload": "await upload_file_content(file_content_base64='...', filename='test.txt', storage_type='minio')", "test_connection": "await test_storage_connection(storage_type='minio')"}}, "migration_guide": {"from_v1": {"description": "从v1.0.0（单一OSS存储）迁移到v2.0.0（多存储支持）", "changes": ["新增storage_type参数，默认为'oss'保持兼容性", "工具函数名称变更：upload_file_to_oss -> upload_file_content", "新增本地存储和MinIO存储支持", "配置方式保持兼容，OSS配置完全兼容v1.0.0"], "compatibility": "向后兼容，原有OSS配置和使用方式仍然有效"}}}