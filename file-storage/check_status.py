#!/usr/bin/env python3
"""
文件存储服务状态检查脚本
检查重构后的多存储架构是否正常工作
"""

import os
import sys
import json
from pathlib import Path

def check_file_structure():
    """检查文件结构"""
    print("📁 检查文件结构...")
    
    required_files = [
        "storage/__init__.py",
        "storage/base.py",
        "storage/config.py", 
        "storage/local.py",
        "storage/oss.py",
        "storage/minio.py",
        "storage/factory.py",
        "server_new.py",
        "test_storage.py",
        "example_usage.py",
        "README_NEW.md",
        "REFACTOR_SUMMARY.md",
        "MIGRATION_GUIDE.md",
        "config_formatted.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"   ✅ {file_path}")
    
    if missing_files:
        print(f"\n   ❌ 缺少文件: {missing_files}")
        return False
    
    print("   ✅ 所有必需文件都存在")
    return True


def check_imports():
    """检查模块导入"""
    print("\n📦 检查模块导入...")
    
    try:
        # 检查存储模块
        from storage.base import BaseStorageProvider, StorageType, StorageResult, FileInfo
        print("   ✅ storage.base 导入成功")
        
        from storage.config import LocalConfigManager, OSSConfigManager, MinIOConfigManager
        print("   ✅ storage.config 导入成功")
        
        from storage.local import LocalStorageProvider
        print("   ✅ storage.local 导入成功")
        
        from storage.oss import OSSStorageProvider
        print("   ✅ storage.oss 导入成功")
        
        from storage.minio import MinIOStorageProvider
        print("   ✅ storage.minio 导入成功")
        
        from storage.factory import StorageFactory, StorageManager
        print("   ✅ storage.factory 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False


def check_storage_availability():
    """检查存储可用性"""
    print("\n🔧 检查存储可用性...")
    
    try:
        from storage.factory import StorageManager
        
        storage_manager = StorageManager()
        storage_info = storage_manager.get_storage_info()
        
        print(f"   默认存储类型: {storage_info['default_storage_type']}")
        
        for provider in storage_info['available_providers']:
            status = '✅ 可用' if provider['available'] else '❌ 不可用'
            print(f"   - {provider['name']}: {status}")
            
            if not provider['available'] and 'error' in provider:
                print(f"     错误: {provider['error']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return False


def check_basic_functionality():
    """检查基本功能"""
    print("\n⚡ 检查基本功能...")
    
    try:
        from storage.factory import StorageManager
        from storage.base import StorageType
        
        storage_manager = StorageManager()
        
        # 测试本地存储连接
        print("   测试本地存储连接...")
        result = storage_manager.test_connection(StorageType.LOCAL)
        if result.success:
            print("   ✅ 本地存储连接正常")
        else:
            print(f"   ❌ 本地存储连接失败: {result.error}")
        
        # 测试OSS存储连接
        print("   测试OSS存储连接...")
        result = storage_manager.test_connection(StorageType.OSS)
        if result.success:
            print("   ✅ OSS存储连接正常")
        else:
            print(f"   ❌ OSS存储连接失败: {result.error}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 功能检查失败: {e}")
        return False


def check_config_files():
    """检查配置文件"""
    print("\n📋 检查配置文件...")
    
    config_files = {
        "config.json": "主配置文件",
        "config_formatted.json": "格式化配置文件"
    }
    
    for file_path, description in config_files.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"   ✅ {description} ({file_path}) - 格式正确")
                
                # 检查关键字段
                if 'display_name' in config and 'version' in config:
                    print(f"      版本: {config.get('version')}")
                    print(f"      名称: {config.get('display_name')}")
                
            except json.JSONDecodeError as e:
                print(f"   ❌ {description} ({file_path}) - JSON格式错误: {e}")
            except Exception as e:
                print(f"   ❌ {description} ({file_path}) - 读取失败: {e}")
        else:
            print(f"   ⚠️ {description} ({file_path}) - 文件不存在")


def check_documentation():
    """检查文档完整性"""
    print("\n📚 检查文档完整性...")
    
    docs = {
        "README_NEW.md": "新架构说明文档",
        "REFACTOR_SUMMARY.md": "重构总结文档", 
        "MIGRATION_GUIDE.md": "迁移指南文档"
    }
    
    for file_path, description in docs.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = len(content.split('\n'))
                    chars = len(content)
                print(f"   ✅ {description} ({file_path}) - {lines} 行, {chars} 字符")
            except Exception as e:
                print(f"   ❌ {description} ({file_path}) - 读取失败: {e}")
        else:
            print(f"   ❌ {description} ({file_path}) - 文件不存在")


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    dependencies = {
        "oss2": "阿里云OSS支持",
        "minio": "MinIO支持（可选）"
    }
    
    for package, description in dependencies.items():
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            if package == "minio":
                print(f"   ⚠️ {package} - {description} (可选，未安装)")
            else:
                print(f"   ❌ {package} - {description} (未安装)")


def generate_status_report():
    """生成状态报告"""
    print("\n📊 生成状态报告...")
    
    try:
        from storage.factory import StorageManager
        
        storage_manager = StorageManager()
        storage_info = storage_manager.get_storage_info()
        
        report = {
            "timestamp": "2025-07-04",
            "version": "2.0.0",
            "status": "重构完成",
            "storage_types": {},
            "features": [
                "多存储支持",
                "统一API接口", 
                "灵活配置管理",
                "易于扩展"
            ]
        }
        
        # 添加存储类型状态
        for provider in storage_info['available_providers']:
            report["storage_types"][provider['type']] = {
                "name": provider['name'],
                "available": provider['available']
            }
        
        # 保存报告
        with open('status_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 状态报告已保存到 status_report.json")
        return True
        
    except Exception as e:
        print(f"   ❌ 生成报告失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 文件存储服务状态检查")
    print("=" * 50)
    
    checks = [
        ("文件结构", check_file_structure),
        ("模块导入", check_imports),
        ("存储可用性", check_storage_availability),
        ("基本功能", check_basic_functionality),
        ("配置文件", check_config_files),
        ("文档完整性", check_documentation),
        ("依赖包", check_dependencies),
        ("状态报告", generate_status_report)
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"   ❌ {name}检查失败: {e}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 检查结果总结:")
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体状态: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查都通过！重构成功完成！")
        return 0
    else:
        print("⚠️ 部分检查未通过，请查看详细信息")
        return 1


if __name__ == "__main__":
    sys.exit(main())
