#!/usr/bin/env python3
"""
测试OSS存储URL一致性
验证上传、列表、获取URL等操作返回的URL格式是否一致
"""

import os
import sys
import tempfile
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from storage.base import StorageType
from storage.factory import StorageManager
from storage.config import OSSConfigManager

def test_oss_url_consistency():
    """测试OSS URL一致性"""
    print("🧪 开始测试OSS URL一致性...")
    
    # 创建存储管理器
    storage_manager = StorageManager()
    
    # 测试配置（使用默认配置）
    test_config = {
        'access_key_id': 'LTAI5tJbjspS4HdX3mGbXqco',
        'endpoint': 'oss-cn-shenzhen.aliyuncs.com',
        'bucket_name': 'coli-ai'
    }
    
    # 测试参数
    expires_in = 7200  # 2小时
    test_content = b"Hello, OSS URL Consistency Test!"
    test_filename = "test_consistency.txt"
    
    print(f"📋 测试配置:")
    print(f"   - 有效期: {expires_in} 秒 ({expires_in//3600}小时)")
    print(f"   - 测试文件: {test_filename}")
    print(f"   - 文件大小: {len(test_content)} 字节")
    print()
    
    try:
        # 1. 测试上传内容
        print("1️⃣ 测试上传内容...")
        upload_result = storage_manager.upload_content(
            content=test_content,
            object_name=test_filename,
            content_type="text/plain",
            storage_type=StorageType.OSS,
            expires_in=expires_in,
            **test_config
        )
        
        if upload_result.success:
            upload_url = upload_result.data.get('url')
            upload_object_name = upload_result.data.get('object_name')
            upload_expires_in = upload_result.data.get('expires_in')
            
            print(f"   ✅ 上传成功")
            print(f"   📄 对象名: {upload_object_name}")
            print(f"   🔗 URL: {upload_url}")
            print(f"   ⏰ 有效期: {upload_expires_in} 秒")
            print()
        else:
            print(f"   ❌ 上传失败: {upload_result.error}")
            return
        
        # 2. 测试列出文件
        print("2️⃣ 测试列出文件...")
        files = storage_manager.list_files(
            prefix="",
            storage_type=StorageType.OSS,
            expires_in=expires_in,
            **test_config
        )
        
        # 查找我们刚上传的文件
        uploaded_file = None
        for file_info in files:
            if file_info.object_name == upload_object_name:
                uploaded_file = file_info
                break
        
        if uploaded_file:
            list_url = uploaded_file.url
            list_expires_in = getattr(uploaded_file, 'expires_in', None)
            
            print(f"   ✅ 找到上传的文件")
            print(f"   📄 对象名: {uploaded_file.object_name}")
            print(f"   🔗 URL: {list_url}")
            print(f"   ⏰ 有效期: {list_expires_in} 秒")
            print()
        else:
            print(f"   ❌ 未找到上传的文件")
            return
        
        # 3. 测试获取文件URL
        print("3️⃣ 测试获取文件URL...")
        get_url = storage_manager.get_file_url(
            object_name=upload_object_name,
            expires_in=expires_in,
            storage_type=StorageType.OSS,
            **test_config
        )
        
        if get_url:
            print(f"   ✅ 获取URL成功")
            print(f"   📄 对象名: {upload_object_name}")
            print(f"   🔗 URL: {get_url}")
            print(f"   ⏰ 有效期: {expires_in} 秒")
            print()
        else:
            print(f"   ❌ 获取URL失败")
            return
        
        # 4. 比较URL一致性
        print("4️⃣ 比较URL一致性...")
        
        # 提取URL的基本部分（去除查询参数）
        def extract_base_url(url):
            if '?' in url:
                return url.split('?')[0]
            return url
        
        upload_base = extract_base_url(upload_url)
        list_base = extract_base_url(list_url)
        get_base = extract_base_url(get_url)
        
        print(f"   📊 URL基础部分比较:")
        print(f"   - 上传URL: {upload_base}")
        print(f"   - 列表URL: {list_base}")
        print(f"   - 获取URL: {get_base}")
        print()
        
        # 检查基础URL是否一致
        if upload_base == list_base == get_base:
            print("   ✅ URL基础部分一致")
        else:
            print("   ❌ URL基础部分不一致")
        
        # 检查是否都是签名URL（包含查询参数）
        has_upload_params = '?' in upload_url and 'Signature' in upload_url
        has_list_params = '?' in list_url and 'Signature' in list_url
        has_get_params = '?' in get_url and 'Signature' in get_url
        
        print(f"   📊 签名URL检查:")
        print(f"   - 上传URL有签名: {has_upload_params}")
        print(f"   - 列表URL有签名: {has_list_params}")
        print(f"   - 获取URL有签名: {has_get_params}")
        print()
        
        if has_upload_params and has_list_params and has_get_params:
            print("   ✅ 所有URL都是签名URL")
        else:
            print("   ❌ 不是所有URL都是签名URL")
        
        # 检查有效期一致性
        print(f"   📊 有效期检查:")
        print(f"   - 上传有效期: {upload_expires_in} 秒")
        print(f"   - 列表有效期: {list_expires_in} 秒")
        print(f"   - 获取有效期: {expires_in} 秒")
        print()
        
        if upload_expires_in == list_expires_in == expires_in:
            print("   ✅ 有效期一致")
        else:
            print("   ❌ 有效期不一致")
        
        # 5. 清理测试文件
        print("5️⃣ 清理测试文件...")
        delete_success = storage_manager.delete_file(
            object_name=upload_object_name,
            storage_type=StorageType.OSS,
            **test_config
        )
        
        if delete_success:
            print("   ✅ 测试文件删除成功")
        else:
            print("   ❌ 测试文件删除失败")
        
        print()
        print("🎉 OSS URL一致性测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_oss_url_consistency()
