#!/usr/bin/env python3
"""
存储服务测试脚本
用于测试不同存储方式的功能
"""

import os
import sys
import base64
import tempfile
from storage.base import StorageType
from storage.factory import StorageManager


def test_local_storage():
    """测试本地存储"""
    print("🧪 测试本地存储...")
    
    storage_manager = StorageManager()
    
    # 测试连接
    print("1. 测试连接...")
    result = storage_manager.test_connection(StorageType.LOCAL)
    print(f"   连接测试: {'✅ 成功' if result.success else '❌ 失败'}")
    if not result.success:
        print(f"   错误: {result.error}")
        return False
    
    # 测试上传内容
    print("2. 测试上传内容...")
    test_content = b"Hello, World! This is a test file."
    result = storage_manager.upload_content(
        content=test_content,
        object_name="test.txt",
        storage_type=StorageType.LOCAL
    )
    print(f"   上传内容: {'✅ 成功' if result.success else '❌ 失败'}")
    if not result.success:
        print(f"   错误: {result.error}")
        return False
    
    uploaded_object_name = result.data.get('object_name')
    
    # 测试列出文件
    print("3. 测试列出文件...")
    files = storage_manager.list_files(storage_type=StorageType.LOCAL)
    print(f"   找到文件数量: {len(files)}")
    
    # 测试获取文件URL
    print("4. 测试获取文件URL...")
    if uploaded_object_name:
        url = storage_manager.get_file_url(
            object_name=uploaded_object_name,
            storage_type=StorageType.LOCAL
        )
        print(f"   文件URL: {url}")
    
    # 测试删除文件
    print("5. 测试删除文件...")
    if uploaded_object_name:
        success = storage_manager.delete_file(
            object_name=uploaded_object_name,
            storage_type=StorageType.LOCAL
        )
        print(f"   删除文件: {'✅ 成功' if success else '❌ 失败'}")
    
    print("✅ 本地存储测试完成\n")
    return True


def test_oss_storage():
    """测试OSS存储"""
    print("🧪 测试OSS存储...")
    
    storage_manager = StorageManager()
    
    # 测试连接
    print("1. 测试连接...")
    result = storage_manager.test_connection(StorageType.OSS)
    print(f"   连接测试: {'✅ 成功' if result.success else '❌ 失败'}")
    if not result.success:
        print(f"   错误: {result.error}")
        return False
    
    # 测试上传内容
    print("2. 测试上传内容...")
    test_content = b"Hello, OSS! This is a test file."
    result = storage_manager.upload_content(
        content=test_content,
        object_name="test_oss.txt",
        storage_type=StorageType.OSS
    )
    print(f"   上传内容: {'✅ 成功' if result.success else '❌ 失败'}")
    if not result.success:
        print(f"   错误: {result.error}")
        return False
    
    uploaded_object_name = result.data.get('object_name')
    
    # 测试列出文件
    print("3. 测试列出文件...")
    files = storage_manager.list_files(storage_type=StorageType.OSS)
    print(f"   找到文件数量: {len(files)}")
    
    # 测试获取文件URL
    print("4. 测试获取文件URL...")
    if uploaded_object_name:
        url = storage_manager.get_file_url(
            object_name=uploaded_object_name,
            storage_type=StorageType.OSS
        )
        print(f"   文件URL: {url[:100]}..." if url and len(url) > 100 else f"   文件URL: {url}")
    
    # 测试删除文件
    print("5. 测试删除文件...")
    if uploaded_object_name:
        success = storage_manager.delete_file(
            object_name=uploaded_object_name,
            storage_type=StorageType.OSS
        )
        print(f"   删除文件: {'✅ 成功' if success else '❌ 失败'}")
    
    print("✅ OSS存储测试完成\n")
    return True


def test_minio_storage():
    """测试MinIO存储"""
    print("🧪 测试MinIO存储...")
    
    storage_manager = StorageManager()
    
    # 测试连接
    print("1. 测试连接...")
    result = storage_manager.test_connection(StorageType.MINIO)
    print(f"   连接测试: {'✅ 成功' if result.success else '❌ 失败'}")
    if not result.success:
        print(f"   错误: {result.error}")
        return False
    
    # 测试上传内容
    print("2. 测试上传内容...")
    test_content = b"Hello, MinIO! This is a test file."
    result = storage_manager.upload_content(
        content=test_content,
        object_name="test_minio.txt",
        storage_type=StorageType.MINIO
    )
    print(f"   上传内容: {'✅ 成功' if result.success else '❌ 失败'}")
    if not result.success:
        print(f"   错误: {result.error}")
        return False
    
    uploaded_object_name = result.data.get('object_name')
    
    # 测试列出文件
    print("3. 测试列出文件...")
    files = storage_manager.list_files(storage_type=StorageType.MINIO)
    print(f"   找到文件数量: {len(files)}")
    
    # 测试获取文件URL
    print("4. 测试获取文件URL...")
    if uploaded_object_name:
        url = storage_manager.get_file_url(
            object_name=uploaded_object_name,
            storage_type=StorageType.MINIO
        )
        print(f"   文件URL: {url[:100]}..." if url and len(url) > 100 else f"   文件URL: {url}")
    
    # 测试删除文件
    print("5. 测试删除文件...")
    if uploaded_object_name:
        success = storage_manager.delete_file(
            object_name=uploaded_object_name,
            storage_type=StorageType.MINIO
        )
        print(f"   删除文件: {'✅ 成功' if success else '❌ 失败'}")
    
    print("✅ MinIO存储测试完成\n")
    return True


def test_storage_info():
    """测试存储信息获取"""
    print("🧪 测试存储信息获取...")
    
    storage_manager = StorageManager()
    storage_info = storage_manager.get_storage_info()
    
    print(f"默认存储类型: {storage_info['default_storage_type']}")
    print("可用存储提供商:")
    
    for provider in storage_info['available_providers']:
        status = '✅ 可用' if provider['available'] else '❌ 不可用'
        print(f"  - {provider['name']}: {status}")
        if not provider['available'] and 'error' in provider:
            print(f"    错误: {provider['error']}")
    
    print("✅ 存储信息获取测试完成\n")


def main():
    """主测试函数"""
    print("🚀 开始存储服务测试\n")
    
    # 测试存储信息
    test_storage_info()
    
    # 测试本地存储
    try:
        test_local_storage()
    except Exception as e:
        print(f"❌ 本地存储测试失败: {str(e)}\n")
    
    # 测试OSS存储
    try:
        test_oss_storage()
    except Exception as e:
        print(f"❌ OSS存储测试失败: {str(e)}\n")
    
    # 测试MinIO存储
    try:
        test_minio_storage()
    except Exception as e:
        print(f"❌ MinIO存储测试失败: {str(e)}\n")
    
    print("🎉 所有测试完成！")


if __name__ == "__main__":
    main()
