# 文件存储 MCP 工具 - 重构版本

支持多种存储方式的文件存储MCP工具，包括本地存储、阿里云OSS、MinIO等。

## 🚀 功能特性

- **多存储支持**: 本地存储、阿里云OSS、MinIO
- **统一接口**: 所有存储方式使用相同的API接口
- **灵活配置**: 支持环境变量、配置文件、参数传递等多种配置方式
- **自动类型检测**: 自动检测文件MIME类型
- **唯一命名**: 自动添加时间戳前缀避免文件名冲突
- **连接测试**: 支持各种存储方式的连接测试
- **错误处理**: 完善的错误处理和用户友好的错误信息

## 📁 项目结构

```
file-storage/
├── storage/                    # 存储模块
│   ├── __init__.py
│   ├── base.py                # 抽象基类和接口定义
│   ├── config.py              # 配置管理
│   ├── local.py               # 本地存储实现
│   ├── oss.py                 # 阿里云OSS存储实现
│   ├── minio.py               # MinIO存储实现
│   └── factory.py             # 存储工厂和管理器
├── server_new.py              # 重构后的主服务文件
├── server.py                  # 原始服务文件（保留）
├── test_storage.py            # 测试脚本
└── README_NEW.md              # 新架构说明文档
```

## 🛠️ 安装依赖

```bash
# 基础依赖
pip install mcp

# OSS存储依赖
pip install oss2

# MinIO存储依赖
pip install minio
```

## ⚙️ 配置方式

### 1. 环境变量配置（推荐）

#### 本地存储
```bash
export LOCAL_STORAGE_BASE_DIR="/path/to/storage"
export LOCAL_STORAGE_CREATE_SUBDIRS="true"
export LOCAL_STORAGE_MAX_FILE_SIZE="104857600"  # 100MB
export LOCAL_STORAGE_ALLOWED_EXTENSIONS=".jpg,.png,.pdf,.txt"
```

#### 阿里云OSS
```bash
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-shenzhen.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"
```

#### MinIO
```bash
export MINIO_ENDPOINT="localhost:9000"
export MINIO_ACCESS_KEY="minioadmin"
export MINIO_SECRET_KEY="minioadmin"
export MINIO_BUCKET_NAME="default"
export MINIO_SECURE="false"
```

### 2. 配置文件

#### 本地存储配置文件 (local_config.json)
```json
{
  "base_dir": "/path/to/storage",
  "create_subdirs": true,
  "max_file_size": 104857600,
  "allowed_extensions": [".jpg", ".png", ".pdf", ".txt"]
}
```

#### OSS配置文件 (oss_config.json)
```json
{
  "access_key_id": "your-access-key-id",
  "access_key_secret": "your-access-key-secret",
  "endpoint": "https://oss-cn-shenzhen.aliyuncs.com",
  "bucket_name": "your-bucket-name"
}
```

#### MinIO配置文件 (minio_config.json)
```json
{
  "endpoint": "localhost:9000",
  "access_key": "minioadmin",
  "secret_key": "minioadmin",
  "bucket_name": "default",
  "secure": false
}
```

## 🔧 工具函数

### upload_file_content
上传Base64编码的文件内容

```python
await upload_file_content(
    file_content_base64="SGVsbG8gV29ybGQ=",
    filename="hello.txt",
    storage_type="local"  # local/oss/minio
)
```

### upload_local_file
上传本地文件

```python
await upload_local_file(
    file_path="/path/to/file.jpg",
    object_name="my-image.jpg",
    storage_type="oss"
)
```

### list_files
列出存储中的文件

```python
await list_files(
    prefix="images/",
    storage_type="minio"
)
```

### delete_file
删除存储中的文件

```python
await delete_file(
    object_name="20231201_120000_test.txt",
    storage_type="local"
)
```

### get_file_url
获取文件访问URL

```python
await get_file_url(
    object_name="20231201_120000_test.txt",
    expires_in=3600,
    storage_type="oss"
)
```

### test_storage_connection
测试存储连接

```python
await test_storage_connection(
    storage_type="minio"
)
```

### get_storage_info
获取存储信息

```python
await get_storage_info()
```

## 🧪 测试

运行测试脚本：

```bash
cd file-storage
python test_storage.py
```

## 🚀 启动服务

```bash
cd file-storage
python server_new.py
```

## 📋 配置优先级

配置的优先级从高到低：

1. **函数参数配置** - 调用工具时传入的参数
2. **环境变量配置** - 系统环境变量
3. **配置文件** - JSON配置文件
4. **默认配置** - 内置默认配置

## 💡 使用建议

1. **开发测试**: 使用本地存储，简单快速
2. **生产环境**: 使用环境变量配置，更安全
3. **个人使用**: 可使用配置文件，方便管理
4. **临时使用**: 通过函数参数传递配置

## ⚠️ 注意事项

1. 确保存储服务已正确配置并可访问
2. 妥善保管访问密钥，不要泄露
3. 配置文件不要提交到版本控制系统
4. 选择合适的存储类型以满足性能和成本需求
5. 本地存储的文件路径需要有写入权限
6. 对象存储的bucket需要预先创建（MinIO除外，会自动创建）

## 🔄 架构说明

新架构采用了以下设计模式：

- **抽象工厂模式**: `StorageFactory` 根据类型创建不同的存储提供商
- **策略模式**: 不同的存储提供商实现相同的接口
- **配置管理**: 统一的配置加载和验证机制
- **错误处理**: 统一的错误处理和结果返回格式

这种设计使得添加新的存储方式变得非常简单，只需要：

1. 实现 `BaseStorageProvider` 接口
2. 创建对应的配置管理器
3. 在 `StorageFactory` 中注册新的提供商

## 🆚 与原版本的区别

- ✅ 支持多种存储方式
- ✅ 统一的接口设计
- ✅ 更好的错误处理
- ✅ 灵活的配置管理
- ✅ 易于扩展新的存储方式
- ✅ 完整的测试覆盖

## 🔮 未来扩展

可以轻松添加更多存储方式：

- AWS S3
- Google Cloud Storage
- Azure Blob Storage
- 腾讯云COS
- 华为云OBS
- FTP/SFTP
- WebDAV

只需要实现对应的存储提供商和配置管理器即可。
