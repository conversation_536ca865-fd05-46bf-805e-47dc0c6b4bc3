#!/usr/bin/env python3
"""
测试本地存储不接受外部参数
验证本地存储方法签名不包含**kwargs，且配置完全只读系统配置
"""

import os
import sys
import inspect

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from storage.base import StorageType
from storage.factory import StorageManager
from storage.local import LocalStorageProvider
from storage.config import LocalConfigManager

def test_local_storage_no_external_params():
    """测试本地存储不接受外部参数"""
    print("🧪 开始测试本地存储不接受外部参数...")
    
    # 创建存储管理器和本地存储提供商
    storage_manager = StorageManager()
    local_provider = LocalStorageProvider()
    
    # 获取uploads目录路径
    uploads_dir = os.path.join(os.path.dirname(__file__), "uploads")
    print(f"📁 预期uploads目录: {uploads_dir}")
    
    try:
        # 1. 测试方法签名不包含**kwargs
        print("\n1️⃣ 测试方法签名不包含**kwargs...")
        
        methods_to_check = [
            ('upload_file', local_provider.upload_file),
            ('upload_content', local_provider.upload_content),
            ('list_files', local_provider.list_files),
            ('delete_file', local_provider.delete_file),
            ('get_file_url', local_provider.get_file_url),
            ('test_connection', local_provider.test_connection)
        ]
        
        all_methods_clean = True
        for method_name, method in methods_to_check:
            sig = inspect.signature(method)
            params = list(sig.parameters.keys())
            print(f"   {method_name}参数: {params}")
            
            if 'kwargs' in params:
                print(f"     ❌ {method_name}方法仍接受**kwargs参数")
                all_methods_clean = False
            else:
                print(f"     ✅ {method_name}方法不接受**kwargs参数")
        
        if all_methods_clean:
            print("   ✅ 所有方法都不接受**kwargs参数")
        else:
            print("   ❌ 部分方法仍接受**kwargs参数")
        
        # 2. 测试配置固定性
        print("\n2️⃣ 测试配置固定性...")
        
        # 测试直接获取配置
        config1 = local_provider.config_manager.get_config()
        base_dir1 = config1.get('base_dir')
        print(f"   直接获取base_dir: {base_dir1}")
        
        # 测试通过参数获取配置（应该被忽略）
        config2 = local_provider.config_manager.get_config(base_dir="/tmp/malicious")
        base_dir2 = config2.get('base_dir')
        print(f"   尝试恶意base_dir后获取: {base_dir2}")
        
        if os.path.abspath(base_dir1) == os.path.abspath(base_dir2) == os.path.abspath(uploads_dir):
            print("   ✅ base_dir配置完全固定，不受外部参数影响")
        else:
            print("   ❌ base_dir配置可能被外部参数影响")
        
        # 3. 测试存储管理器正确处理本地存储
        print("\n3️⃣ 测试存储管理器正确处理本地存储...")
        
        # 测试上传文件（不传递外部参数）
        test_content = b"Test content for local storage"
        test_filename = "test_no_external_params.txt"
        
        result = storage_manager.upload_content(
            content=test_content,
            object_name=test_filename,
            storage_type=StorageType.LOCAL
        )
        
        if result.success:
            print("   ✅ 本地存储上传成功（无外部参数）")
            uploaded_object_name = result.data.get('object_name')
            uploaded_url = result.data.get('url')
            
            print(f"   📄 对象名: {uploaded_object_name}")
            print(f"   🔗 URL: {uploaded_url}")
            
            # 验证文件在uploads目录内
            if uploaded_url and uploaded_url.startswith('file://'):
                file_path = uploaded_url[7:]  # 移除 'file://' 前缀
                abs_file_path = os.path.abspath(file_path)
                abs_uploads_dir = os.path.abspath(uploads_dir)
                
                if abs_file_path.startswith(abs_uploads_dir):
                    print("   ✅ 文件确实保存在uploads目录内")
                else:
                    print(f"   ❌ 文件保存在uploads目录外: {abs_file_path}")
            
            # 4. 测试文件列表
            print("\n4️⃣ 测试文件列表...")
            
            files = storage_manager.list_files(
                storage_type=StorageType.LOCAL
            )
            
            found_file = False
            for file_info in files:
                if file_info.object_name == uploaded_object_name:
                    found_file = True
                    print(f"   ✅ 找到上传的文件: {file_info.object_name}")
                    break
            
            if not found_file:
                print("   ❌ 未在文件列表中找到上传的文件")
            
            # 5. 测试获取文件URL
            print("\n5️⃣ 测试获取文件URL...")
            
            file_url = storage_manager.get_file_url(
                object_name=uploaded_object_name,
                storage_type=StorageType.LOCAL
            )
            
            if file_url:
                print(f"   ✅ 获取文件URL成功: {file_url}")
                
                # 验证URL指向uploads目录内的文件
                if file_url.startswith('file://'):
                    url_file_path = file_url[7:]
                    abs_url_file_path = os.path.abspath(url_file_path)
                    abs_uploads_dir = os.path.abspath(uploads_dir)
                    
                    if abs_url_file_path.startswith(abs_uploads_dir):
                        print("   ✅ URL指向uploads目录内的文件")
                    else:
                        print(f"   ❌ URL指向uploads目录外的文件: {abs_url_file_path}")
            else:
                print("   ❌ 获取文件URL失败")
            
            # 6. 测试连接测试
            print("\n6️⃣ 测试连接测试...")
            
            test_result = storage_manager.test_connection(StorageType.LOCAL)
            
            if test_result.success:
                print("   ✅ 连接测试成功")
                if test_result.data:
                    reported_base_dir = test_result.data.get('base_dir')
                    print(f"   📁 报告的base_dir: {reported_base_dir}")
                    
                    if os.path.abspath(reported_base_dir) == os.path.abspath(uploads_dir):
                        print("   ✅ 连接测试报告的base_dir正确")
                    else:
                        print("   ❌ 连接测试报告的base_dir不正确")
            else:
                print(f"   ❌ 连接测试失败: {test_result.error}")
            
            # 7. 清理测试文件
            print("\n7️⃣ 清理测试文件...")
            
            delete_success = storage_manager.delete_file(
                object_name=uploaded_object_name,
                storage_type=StorageType.LOCAL
            )
            
            if delete_success:
                print("   ✅ 测试文件删除成功")
            else:
                print("   ❌ 测试文件删除失败")
        
        else:
            print(f"   ❌ 本地存储上传失败: {result.error}")
        
        # 8. 测试默认存储类型处理
        print("\n8️⃣ 测试默认存储类型处理...")
        
        # 如果默认存储类型是LOCAL，测试不传storage_type参数
        if storage_manager.default_storage_type == StorageType.LOCAL:
            print("   默认存储类型是LOCAL，测试不传storage_type参数...")
            
            result = storage_manager.upload_content(
                content=b"Test default storage type",
                object_name="test_default.txt"
            )
            
            if result.success:
                print("   ✅ 默认存储类型上传成功")
                
                # 清理
                storage_manager.delete_file(
                    object_name=result.data.get('object_name')
                )
            else:
                print(f"   ❌ 默认存储类型上传失败: {result.error}")
        else:
            print(f"   默认存储类型是{storage_manager.default_storage_type}，跳过此测试")
        
        print("\n🎉 本地存储不接受外部参数测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_local_storage_no_external_params()
