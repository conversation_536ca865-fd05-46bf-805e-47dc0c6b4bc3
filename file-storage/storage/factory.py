#!/usr/bin/env python3
"""
存储工厂类
根据配置创建不同类型的存储提供商
"""

import os
from typing import Dict, Any, Optional, List
from .base import BaseStorageProvider, StorageType
from .local import LocalStorageProvider
from .oss import OSSStorageProvider
from .minio import MinIOStorageProvider
from .config import LocalConfigManager, OSSConfigManager, MinIOConfigManager


class StorageFactory:
    """存储工厂类"""
    
    # 存储提供商映射
    PROVIDERS = {
        StorageType.LOCAL: {
            'class': LocalStorageProvider,
            'config_manager': LocalConfigManager
        },
        StorageType.OSS: {
            'class': OSSStorageProvider,
            'config_manager': OSSConfigManager
        },
        StorageType.MINIO: {
            'class': MinIOStorageProvider,
            'config_manager': MinIOConfigManager
        }
    }
    
    @classmethod
    def create_provider(cls, storage_type: StorageType, config_manager: Optional[Any] = None) -> BaseStorageProvider:
        """创建存储提供商
        
        Args:
            storage_type: 存储类型
            config_manager: 配置管理器（可选）
            
        Returns:
            BaseStorageProvider: 存储提供商实例
            
        Raises:
            ValueError: 不支持的存储类型
        """
        if storage_type not in cls.PROVIDERS:
            raise ValueError(f"不支持的存储类型: {storage_type}")
        
        provider_info = cls.PROVIDERS[storage_type]
        provider_class = provider_info['class']
        
        # 如果没有提供配置管理器，创建默认的
        if config_manager is None:
            config_manager_class = provider_info['config_manager']
            config_manager = config_manager_class()
        
        return provider_class(config_manager)
    
    @classmethod
    def get_default_storage_type(cls) -> StorageType:
        """获取默认存储类型
        
        Returns:
            StorageType: 默认存储类型
        """
        # 从环境变量获取默认存储类型
        default_type = os.getenv('DEFAULT_STORAGE_TYPE', 'oss').lower()
        
        try:
            return StorageType(default_type)
        except ValueError:
            return StorageType.OSS
    
    @classmethod
    def get_available_providers(cls) -> List[Dict[str, Any]]:
        """获取可用的存储提供商列表
        
        Returns:
            List[Dict[str, Any]]: 可用提供商信息列表
        """
        available_providers = []
        
        for storage_type, provider_info in cls.PROVIDERS.items():
            try:
                provider = cls.create_provider(storage_type)
                available_providers.append({
                    'type': storage_type.value,
                    'name': storage_type.value.upper(),
                    'available': provider.is_available(),
                    'provider_info': provider.get_provider_info()
                })
            except Exception as e:
                available_providers.append({
                    'type': storage_type.value,
                    'name': storage_type.value.upper(),
                    'available': False,
                    'error': str(e)
                })
        
        return available_providers


class StorageManager:
    """统一存储管理器"""
    
    def __init__(self, default_storage_type: Optional[StorageType] = None):
        """初始化存储管理器
        
        Args:
            default_storage_type: 默认存储类型
        """
        self.default_storage_type = default_storage_type or StorageFactory.get_default_storage_type()
        self._providers = {}
    
    def get_provider(self, storage_type: Optional[StorageType] = None) -> BaseStorageProvider:
        """获取存储提供商
        
        Args:
            storage_type: 存储类型，如果为None则使用默认类型
            
        Returns:
            BaseStorageProvider: 存储提供商实例
        """
        if storage_type is None:
            storage_type = self.default_storage_type
        
        # 缓存提供商实例
        if storage_type not in self._providers:
            self._providers[storage_type] = StorageFactory.create_provider(storage_type)
        
        return self._providers[storage_type]
    
    def upload_file(self, file_path: str, object_name: Optional[str] = None, 
                   storage_type: Optional[StorageType] = None, **kwargs):
        """上传文件
        
        Args:
            file_path: 文件路径
            object_name: 对象名
            storage_type: 存储类型
            **kwargs: 额外参数
            
        Returns:
            StorageResult: 上传结果
        """
        provider = self.get_provider(storage_type)
        return provider.upload_file(file_path, object_name, **kwargs)
    
    def upload_content(self, content: bytes, object_name: str, content_type: Optional[str] = None,
                      storage_type: Optional[StorageType] = None, **kwargs):
        """上传内容
        
        Args:
            content: 文件内容
            object_name: 对象名
            content_type: 内容类型
            storage_type: 存储类型
            **kwargs: 额外参数
            
        Returns:
            StorageResult: 上传结果
        """
        provider = self.get_provider(storage_type)
        return provider.upload_content(content, object_name, content_type, **kwargs)
    
    def list_files(self, prefix: str = "", storage_type: Optional[StorageType] = None, **kwargs):
        """列出文件
        
        Args:
            prefix: 文件名前缀
            storage_type: 存储类型
            **kwargs: 额外参数
            
        Returns:
            List[FileInfo]: 文件信息列表
        """
        provider = self.get_provider(storage_type)
        return provider.list_files(prefix, **kwargs)
    
    def delete_file(self, object_name: str, storage_type: Optional[StorageType] = None, **kwargs):
        """删除文件
        
        Args:
            object_name: 对象名
            storage_type: 存储类型
            **kwargs: 额外参数
            
        Returns:
            bool: 是否删除成功
        """
        provider = self.get_provider(storage_type)
        return provider.delete_file(object_name, **kwargs)
    
    def get_file_url(self, object_name: str, expires_in: int = 3600,
                    storage_type: Optional[StorageType] = None, **kwargs):
        """获取文件URL
        
        Args:
            object_name: 对象名
            expires_in: 过期时间
            storage_type: 存储类型
            **kwargs: 额外参数
            
        Returns:
            Optional[str]: 文件URL
        """
        provider = self.get_provider(storage_type)
        return provider.get_file_url(object_name, expires_in, **kwargs)
    
    def test_connection(self, storage_type: Optional[StorageType] = None, **kwargs):
        """测试连接
        
        Args:
            storage_type: 存储类型
            **kwargs: 额外参数
            
        Returns:
            StorageResult: 测试结果
        """
        provider = self.get_provider(storage_type)
        return provider.test_connection(**kwargs)
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息
        
        Returns:
            Dict[str, Any]: 存储信息
        """
        return {
            'default_storage_type': self.default_storage_type.value,
            'available_providers': StorageFactory.get_available_providers()
        }
