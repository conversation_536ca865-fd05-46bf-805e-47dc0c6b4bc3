#!/usr/bin/env python3
"""
统一配置管理
支持多种存储方式的配置加载和管理
"""

import os
import json
from typing import Dict, Any, Optional
from .base import BaseConfigManager, StorageType


class LocalConfigManager(BaseConfigManager):
    """本地存储配置管理器"""
    
    def __init__(self):
        super().__init__(StorageType.LOCAL)
    
    def _load_config(self):
        """加载本地存储配置"""
        # 默认配置
        self.config = {
            'base_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), "uploads"),
            'create_subdirs': True,
            'max_file_size': 100 * 1024 * 1024,  # 100MB
            'allowed_extensions': None  # None表示允许所有扩展名
        }
        
        # 从环境变量加载
        env_mapping = {
            'LOCAL_STORAGE_BASE_DIR': 'base_dir',
            'LOCAL_STORAGE_CREATE_SUBDIRS': 'create_subdirs',
            'LOCAL_STORAGE_MAX_FILE_SIZE': 'max_file_size',
            'LOCAL_STORAGE_ALLOWED_EXTENSIONS': 'allowed_extensions'
        }
        
        for env_key, config_key in env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value:
                if config_key == 'create_subdirs':
                    self.config[config_key] = env_value.lower() in ('true', '1', 'yes')
                elif config_key == 'max_file_size':
                    self.config[config_key] = int(env_value)
                elif config_key == 'allowed_extensions':
                    self.config[config_key] = env_value.split(',') if env_value else None
                else:
                    self.config[config_key] = env_value
        
        # 从配置文件加载
        config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'local_config.json')
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
            except Exception as e:
                print(f"警告: 无法加载本地存储配置文件 {config_file}: {e}")
    
    def get_config(self, **kwargs) -> Dict[str, Any]:
        """获取最终配置"""
        final_config = self.config.copy()
        
        # 参数覆盖
        param_mapping = {
            'base_dir': 'base_dir',
            'create_subdirs': 'create_subdirs',
            'max_file_size': 'max_file_size',
            'allowed_extensions': 'allowed_extensions'
        }
        
        for param_key, config_key in param_mapping.items():
            if param_key in kwargs and kwargs[param_key] is not None:
                final_config[config_key] = kwargs[param_key]
        
        return final_config
    
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证本地存储配置"""
        required_keys = ['base_dir']
        
        for key in required_keys:
            if not config.get(key):
                return False, f"缺少必需的配置项: {key}"
        
        # 验证目录是否可创建
        base_dir = config['base_dir']
        try:
            os.makedirs(base_dir, exist_ok=True)
            if not os.access(base_dir, os.W_OK):
                return False, f"目录不可写: {base_dir}"
        except Exception as e:
            return False, f"无法创建或访问目录 {base_dir}: {str(e)}"
        
        return True, "配置验证通过"


class OSSConfigManager(BaseConfigManager):
    """阿里云OSS配置管理器"""
    
    # 默认SSO配置
    DEFAULT_CONFIG = {
        'access_key_id': 'LTAI5tJbjspS4HdX3mGbXqco',
        'access_key_secret': '******************************',
        'endpoint': 'https://oss-cn-shenzhen.aliyuncs.com',
        'bucket_name': 'coli-ai'
    }
    
    def __init__(self):
        super().__init__(StorageType.OSS)
    
    def _load_config(self):
        """加载OSS配置"""
        # 从默认配置开始
        self.config = self.DEFAULT_CONFIG.copy()
        
        # 从配置文件加载
        config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'oss_config.json')
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
            except Exception as e:
                print(f"警告: 无法加载OSS配置文件 {config_file}: {e}")
        
        # 环境变量覆盖
        env_mapping = {
            'OSS_ACCESS_KEY_ID': 'access_key_id',
            'OSS_ACCESS_KEY_SECRET': 'access_key_secret',
            'OSS_ENDPOINT': 'endpoint',
            'OSS_BUCKET_NAME': 'bucket_name'
        }
        
        for env_key, config_key in env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value:
                self.config[config_key] = env_value
    
    def get_config(self, **kwargs) -> Dict[str, Any]:
        """获取最终配置"""
        final_config = self.config.copy()
        
        # 参数映射
        param_mapping = {
            'access_key_id': 'access_key_id',
            'access_key_secret': 'access_key_secret',
            'endpoint': 'endpoint',
            'bucket_name': 'bucket_name'
        }
        
        for param_key, config_key in param_mapping.items():
            if param_key in kwargs and kwargs[param_key]:
                final_config[config_key] = kwargs[param_key]
        
        return final_config
    
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证OSS配置"""
        required_keys = ['access_key_id', 'access_key_secret', 'endpoint', 'bucket_name']
        
        for key in required_keys:
            if not config.get(key):
                return False, f"缺少必需的配置项: {key}"
        
        # 验证endpoint格式
        endpoint = config['endpoint']
        if not endpoint.startswith(('http://', 'https://')):
            config['endpoint'] = f"https://{endpoint}"
        
        return True, "配置验证通过"


class MinIOConfigManager(BaseConfigManager):
    """MinIO配置管理器"""
    
    def __init__(self):
        super().__init__(StorageType.MINIO)
    
    def _load_config(self):
        """加载MinIO配置"""
        # 默认配置
        self.config = {
            'endpoint': 'localhost:9000',
            'access_key': 'minioadmin',
            'secret_key': 'minioadmin',
            'bucket_name': 'default',
            'secure': False
        }
        
        # 从配置文件加载
        config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'minio_config.json')
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
            except Exception as e:
                print(f"警告: 无法加载MinIO配置文件 {config_file}: {e}")
        
        # 环境变量覆盖
        env_mapping = {
            'MINIO_ENDPOINT': 'endpoint',
            'MINIO_ACCESS_KEY': 'access_key',
            'MINIO_SECRET_KEY': 'secret_key',
            'MINIO_BUCKET_NAME': 'bucket_name',
            'MINIO_SECURE': 'secure'
        }
        
        for env_key, config_key in env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value:
                if config_key == 'secure':
                    self.config[config_key] = env_value.lower() in ('true', '1', 'yes')
                else:
                    self.config[config_key] = env_value
    
    def get_config(self, **kwargs) -> Dict[str, Any]:
        """获取最终配置"""
        final_config = self.config.copy()
        
        # 参数映射
        param_mapping = {
            'endpoint': 'endpoint',
            'access_key': 'access_key',
            'secret_key': 'secret_key',
            'bucket_name': 'bucket_name',
            'secure': 'secure'
        }
        
        for param_key, config_key in param_mapping.items():
            if param_key in kwargs and kwargs[param_key] is not None:
                final_config[config_key] = kwargs[param_key]
        
        return final_config
    
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证MinIO配置"""
        required_keys = ['endpoint', 'access_key', 'secret_key', 'bucket_name']
        
        for key in required_keys:
            if not config.get(key):
                return False, f"缺少必需的配置项: {key}"
        
        return True, "配置验证通过"
