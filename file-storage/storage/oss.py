#!/usr/bin/env python3
"""
阿里云OSS存储实现
支持阿里云对象存储服务的文件存储操作
"""

import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from .base import BaseStorageProvider, StorageResult, FileInfo
from .config import OSSConfigManager


class OSSStorageProvider(BaseStorageProvider):
    """阿里云OSS存储提供商"""
    
    def __init__(self, config_manager: Optional[OSSConfigManager] = None):
        """初始化OSS存储提供商
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or OSSConfigManager()
        super().__init__(self.config_manager.config)
        self._bucket = None
        self._current_config = None
        
        # 导入oss2库
        try:
            import oss2
            self.oss2 = oss2
            self._available = True
        except ImportError:
            self.oss2 = None
            self._available = False
    
    def is_available(self) -> bool:
        """检查OSS是否可用"""
        return self._available and self.oss2 is not None
    
    def _get_bucket(self, **kwargs):
        """获取OSS bucket实例"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")
        
        # 获取最终配置
        config = self.config_manager.get_config(**kwargs)
        
        # 验证配置
        is_valid, message = self.config_manager.validate_config(config)
        if not is_valid:
            raise ValueError(f"OSS配置错误: {message}")
        
        # 如果配置发生变化，重新创建bucket实例
        if self._bucket is None or self._current_config != config:
            try:
                auth = self.oss2.Auth(config['access_key_id'], config['access_key_secret'])
                self._bucket = self.oss2.Bucket(auth, config['endpoint'], config['bucket_name'])
                self._current_config = config.copy()
            except Exception as e:
                raise ValueError(f"创建OSS连接失败: {str(e)}")
        
        return self._bucket
    
    def _generate_object_name(self, original_name: str) -> str:
        """生成对象名（添加时间戳前缀避免重名）
        
        Args:
            original_name: 原始文件名
            
        Returns:
            str: 生成的对象名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{original_name}"
    
    def upload_file(self, file_path: str, object_name: Optional[str] = None, **kwargs) -> StorageResult:
        """上传本地文件到OSS"""
        try:
            if not os.path.exists(file_path):
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"源文件不存在: {file_path}"
                )

            bucket = self._get_bucket(**kwargs)

            if object_name is None:
                object_name = os.path.basename(file_path)

            # 生成唯一对象名
            unique_object_name = self._generate_object_name(object_name)

            # 上传文件
            result = bucket.put_object_from_file(unique_object_name, file_path)

            # 获取URL有效期（默认1小时）
            expires_in = kwargs.get('expires_in', 3600)

            # 生成签名URL
            url = bucket.sign_url('GET', unique_object_name, expires_in)

            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="文件上传成功",
                data={
                    "object_name": unique_object_name,
                    "etag": result.etag,
                    "url": url,
                    "expires_in": expires_in,
                    "size": os.path.getsize(file_path)
                }
            )

        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"上传文件失败: {str(e)}"
            )
    
    def upload_content(self, content: bytes, object_name: str, content_type: Optional[str] = None, **kwargs) -> StorageResult:
        """上传文件内容到OSS"""
        try:
            bucket = self._get_bucket(**kwargs)
            
            # 生成唯一对象名
            unique_object_name = self._generate_object_name(object_name)
            
            # 设置headers
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type
            
            # 上传内容
            result = bucket.put_object(unique_object_name, content, headers=headers)
            
            # 构建访问URL
            url = f"https://{bucket.bucket_name}.{bucket.endpoint.replace('https://', '').replace('http://', '')}/{unique_object_name}"
            
            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="内容上传成功",
                data={
                    "object_name": unique_object_name,
                    "etag": result.etag,
                    "url": url,
                    "size": len(content)
                }
            )
            
        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"上传内容失败: {str(e)}"
            )
    
    def list_files(self, prefix: str = "", **kwargs) -> List[FileInfo]:
        """列出OSS文件"""
        try:
            bucket = self._get_bucket(**kwargs)
            
            files = []
            for obj in self.oss2.ObjectIterator(bucket, prefix=prefix):
                # 处理日期格式化
                modified_date = ""
                if obj.last_modified:
                    if hasattr(obj.last_modified, 'isoformat'):
                        modified_date = obj.last_modified.isoformat()
                    else:
                        modified_date = str(obj.last_modified)

                file_info = FileInfo(
                    name=obj.key,
                    size=obj.size,
                    mime_type="",  # OSS不直接提供MIME类型
                    created="",
                    modified=modified_date,
                    object_name=obj.key,
                    etag=obj.etag,
                    url=f"https://{bucket.bucket_name}.{bucket.endpoint.replace('https://', '').replace('http://', '')}/{obj.key}"
                )
                files.append(file_info)
            
            return files
            
        except Exception as e:
            print(f"列出OSS文件失败: {str(e)}")
            return []
    
    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除OSS文件"""
        try:
            bucket = self._get_bucket(**kwargs)
            bucket.delete_object(object_name)
            return True
            
        except Exception as e:
            print(f"删除OSS文件失败: {str(e)}")
            return False
    
    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> Optional[str]:
        """获取OSS文件访问URL"""
        try:
            bucket = self._get_bucket(**kwargs)
            url = bucket.sign_url('GET', object_name, expires_in)
            return url
            
        except Exception as e:
            print(f"获取OSS文件URL失败: {str(e)}")
            return None
    
    def test_connection(self, **kwargs) -> StorageResult:
        """测试OSS连接"""
        try:
            if not self.is_available():
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error="OSS不可用，请安装oss2库"
                )
            
            # 获取配置信息
            config = self.config_manager.get_config(**kwargs)
            is_valid, message = self.config_manager.validate_config(config)
            
            if not is_valid:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"配置验证失败: {message}"
                )
            
            # 尝试连接OSS
            bucket = self._get_bucket(**kwargs)
            
            # 获取bucket信息
            bucket_info = bucket.get_bucket_info()
            
            # 统计文件数量
            file_count = 0
            try:
                for _ in self.oss2.ObjectIterator(bucket, max_keys=1000):
                    file_count += 1
            except Exception:
                file_count = 0
            
            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="OSS连接测试成功",
                data={
                    "bucket_name": bucket_info.name,
                    "creation_date": bucket_info.creation_date.isoformat() if hasattr(bucket_info.creation_date, 'isoformat') else str(bucket_info.creation_date),
                    "storage_class": bucket_info.storage_class,
                    "location": bucket_info.location,
                    "file_count": file_count
                }
            )
            
        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"OSS连接测试失败: {str(e)}"
            )
