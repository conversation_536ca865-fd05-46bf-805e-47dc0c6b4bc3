#!/usr/bin/env python3
"""
本地文件存储实现
支持本地文件系统的文件存储操作
"""

import os
import shutil
import hashlib
import mimetypes
from datetime import datetime
from typing import Dict, Any, List, Optional
from urllib.parse import quote

from .base import BaseStorageProvider, StorageResult, FileInfo
from .config import LocalConfigManager


class LocalStorageProvider(BaseStorageProvider):
    """本地文件存储提供商"""
    
    def __init__(self, config_manager: Optional[LocalConfigManager] = None):
        """初始化本地存储提供商
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or LocalConfigManager()
        super().__init__(self.config_manager.config)
        self._ensure_base_dir()
    
    def _ensure_base_dir(self):
        """确保基础目录存在"""
        base_dir = self.config.get('base_dir')
        if base_dir:
            os.makedirs(base_dir, exist_ok=True)
    
    def is_available(self) -> bool:
        """检查本地存储是否可用"""
        try:
            base_dir = self.config.get('base_dir')
            if not base_dir:
                return False
            
            # 检查目录是否存在且可写
            os.makedirs(base_dir, exist_ok=True)
            return os.access(base_dir, os.W_OK)
        except Exception:
            return False
    
    def _get_file_path(self, object_name: str) -> str:
        """获取文件的完整路径
        
        Args:
            object_name: 对象名
            
        Returns:
            str: 完整文件路径
        """
        base_dir = self.config.get('base_dir')
        
        # 如果启用子目录创建，根据日期创建子目录
        if self.config.get('create_subdirs', True):
            date_dir = datetime.now().strftime("%Y/%m/%d")
            full_dir = os.path.join(base_dir, date_dir)
            os.makedirs(full_dir, exist_ok=True)
            return os.path.join(full_dir, object_name)
        else:
            return os.path.join(base_dir, object_name)
    
    def _generate_unique_name(self, original_name: str) -> str:
        """生成唯一的文件名
        
        Args:
            original_name: 原始文件名
            
        Returns:
            str: 唯一文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 毫秒精度
        name, ext = os.path.splitext(original_name)
        return f"{timestamp}_{name}{ext}"
    
    def _calculate_md5(self, file_path: str) -> str:
        """计算文件MD5哈希
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: MD5哈希值
        """
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _get_file_info(self, file_path: str, object_name: str) -> FileInfo:
        """获取文件信息
        
        Args:
            file_path: 文件路径
            object_name: 对象名
            
        Returns:
            FileInfo: 文件信息
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        stat = os.stat(file_path)
        mime_type, _ = mimetypes.guess_type(file_path)
        
        return FileInfo(
            name=os.path.basename(file_path),
            size=stat.st_size,
            mime_type=mime_type or "application/octet-stream",
            md5=self._calculate_md5(file_path),
            created=datetime.fromtimestamp(stat.st_ctime).isoformat(),
            modified=datetime.fromtimestamp(stat.st_mtime).isoformat(),
            object_name=object_name,
            url=f"file://{os.path.abspath(file_path)}"
        )
    
    def upload_file(self, file_path: str, object_name: Optional[str] = None, **kwargs) -> StorageResult:
        """上传本地文件"""
        try:
            if not os.path.exists(file_path):
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"源文件不存在: {file_path}"
                )
            
            # 获取配置
            config = self.config_manager.get_config(**kwargs)
            is_valid, message = self.config_manager.validate_config(config)
            if not is_valid:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"配置错误: {message}"
                )
            
            # 生成对象名
            if object_name is None:
                object_name = os.path.basename(file_path)
            
            # 生成唯一文件名
            unique_name = self._generate_unique_name(object_name)
            target_path = self._get_file_path(unique_name)
            
            # 检查文件大小限制
            file_size = os.path.getsize(file_path)
            max_size = config.get('max_file_size')
            if max_size and file_size > max_size:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"文件大小超过限制: {file_size} > {max_size}"
                )
            
            # 检查文件扩展名
            allowed_extensions = config.get('allowed_extensions')
            if allowed_extensions:
                _, ext = os.path.splitext(object_name)
                if ext.lower() not in [e.lower() for e in allowed_extensions]:
                    return StorageResult(
                        success=False,
                        provider=self.provider_name,
                        error=f"不支持的文件扩展名: {ext}"
                    )
            
            # 复制文件
            shutil.copy2(file_path, target_path)
            
            # 获取文件信息
            file_info = self._get_file_info(target_path, unique_name)
            
            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="文件上传成功",
                data={
                    "object_name": unique_name,
                    "size": file_info.size,
                    "md5": file_info.md5,
                    "url": file_info.url,
                    "mime_type": file_info.mime_type
                }
            )
            
        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"上传文件失败: {str(e)}"
            )
    
    def upload_content(self, content: bytes, object_name: str, content_type: Optional[str] = None, **kwargs) -> StorageResult:
        """上传文件内容"""
        try:
            # 获取配置
            config = self.config_manager.get_config(**kwargs)
            is_valid, message = self.config_manager.validate_config(config)
            if not is_valid:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"配置错误: {message}"
                )
            
            # 检查内容大小限制
            content_size = len(content)
            max_size = config.get('max_file_size')
            if max_size and content_size > max_size:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"内容大小超过限制: {content_size} > {max_size}"
                )
            
            # 检查文件扩展名
            allowed_extensions = config.get('allowed_extensions')
            if allowed_extensions:
                _, ext = os.path.splitext(object_name)
                if ext.lower() not in [e.lower() for e in allowed_extensions]:
                    return StorageResult(
                        success=False,
                        provider=self.provider_name,
                        error=f"不支持的文件扩展名: {ext}"
                    )
            
            # 生成唯一文件名
            unique_name = self._generate_unique_name(object_name)
            target_path = self._get_file_path(unique_name)
            
            # 写入文件
            with open(target_path, 'wb') as f:
                f.write(content)
            
            # 获取文件信息
            file_info = self._get_file_info(target_path, unique_name)
            
            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="内容上传成功",
                data={
                    "object_name": unique_name,
                    "size": file_info.size,
                    "md5": file_info.md5,
                    "url": file_info.url,
                    "mime_type": content_type or file_info.mime_type
                }
            )
            
        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"上传内容失败: {str(e)}"
            )

    def list_files(self, prefix: str = "", **kwargs) -> List[FileInfo]:
        """列出文件"""
        try:
            config = self.config_manager.get_config(**kwargs)
            base_dir = config.get('base_dir')

            files = []

            # 遍历目录
            for root, dirs, filenames in os.walk(base_dir):
                for filename in filenames:
                    # 应用前缀过滤
                    if prefix and not filename.startswith(prefix):
                        continue

                    file_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(file_path, base_dir)

                    try:
                        file_info = self._get_file_info(file_path, relative_path)
                        files.append(file_info)
                    except Exception as e:
                        print(f"警告: 无法获取文件信息 {file_path}: {e}")
                        continue

            return files

        except Exception as e:
            print(f"列出文件失败: {str(e)}")
            return []

    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除文件"""
        try:
            config = self.config_manager.get_config(**kwargs)
            base_dir = config.get('base_dir')

            # 查找文件
            file_path = None
            for root, dirs, filenames in os.walk(base_dir):
                for filename in filenames:
                    full_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(full_path, base_dir)

                    if relative_path == object_name or filename == object_name:
                        file_path = full_path
                        break

                if file_path:
                    break

            if not file_path or not os.path.exists(file_path):
                return False

            os.remove(file_path)

            # 如果目录为空，删除目录
            parent_dir = os.path.dirname(file_path)
            if parent_dir != base_dir and not os.listdir(parent_dir):
                try:
                    os.rmdir(parent_dir)
                except OSError:
                    pass  # 目录不为空或无法删除

            return True

        except Exception as e:
            print(f"删除文件失败: {str(e)}")
            return False

    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> Optional[str]:
        """获取文件访问URL"""
        try:
            config = self.config_manager.get_config(**kwargs)
            base_dir = config.get('base_dir')

            # 查找文件
            for root, dirs, filenames in os.walk(base_dir):
                for filename in filenames:
                    full_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(full_path, base_dir)

                    if relative_path == object_name or filename == object_name:
                        return f"file://{os.path.abspath(full_path)}"

            return None

        except Exception as e:
            print(f"获取文件URL失败: {str(e)}")
            return None

    def test_connection(self, **kwargs) -> StorageResult:
        """测试本地存储连接"""
        try:
            config = self.config_manager.get_config(**kwargs)
            is_valid, message = self.config_manager.validate_config(config)

            if not is_valid:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"配置验证失败: {message}"
                )

            base_dir = config.get('base_dir')

            # 测试目录访问
            if not os.path.exists(base_dir):
                os.makedirs(base_dir, exist_ok=True)

            if not os.access(base_dir, os.W_OK):
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"目录不可写: {base_dir}"
                )

            # 测试写入文件
            test_file = os.path.join(base_dir, '.storage_test')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
            except Exception as e:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"无法写入测试文件: {str(e)}"
                )

            # 统计文件数量
            file_count = 0
            total_size = 0
            for root, dirs, filenames in os.walk(base_dir):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    try:
                        file_count += 1
                        total_size += os.path.getsize(file_path)
                    except OSError:
                        continue

            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="本地存储连接测试成功",
                data={
                    "base_dir": base_dir,
                    "file_count": file_count,
                    "total_size": total_size,
                    "writable": True
                }
            )

        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"连接测试失败: {str(e)}"
            )
