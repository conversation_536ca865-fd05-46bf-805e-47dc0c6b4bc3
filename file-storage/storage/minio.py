#!/usr/bin/env python3
"""
MinIO存储实现
支持MinIO对象存储服务的文件存储操作
"""

import os
import mimetypes
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from .base import BaseStorageProvider, StorageResult, FileInfo
from .config import MinIOConfigManager


class MinIOStorageProvider(BaseStorageProvider):
    """MinIO存储提供商"""
    
    def __init__(self, config_manager: Optional[MinIOConfigManager] = None):
        """初始化MinIO存储提供商
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or MinIOConfigManager()
        super().__init__(self.config_manager.config)
        self._client = None
        self._current_config = None
        
        # 导入minio库
        try:
            from minio import Minio
            from minio.error import S3Error
            self.Minio = Minio
            self.S3Error = S3Error
            self._available = True
        except ImportError:
            self.Minio = None
            self.S3Error = None
            self._available = False
    
    def is_available(self) -> bool:
        """检查MinIO是否可用"""
        return self._available and self.Minio is not None
    
    def _get_client(self, **kwargs):
        """获取MinIO客户端实例"""
        if not self.is_available():
            raise ValueError("MinIO不可用，请安装minio库")
        
        # 获取最终配置
        config = self.config_manager.get_config(**kwargs)
        
        # 验证配置
        is_valid, message = self.config_manager.validate_config(config)
        if not is_valid:
            raise ValueError(f"MinIO配置错误: {message}")
        
        # 如果配置发生变化，重新创建客户端实例
        if self._client is None or self._current_config != config:
            try:
                self._client = self.Minio(
                    endpoint=config['endpoint'],
                    access_key=config['access_key'],
                    secret_key=config['secret_key'],
                    secure=config.get('secure', False)
                )
                self._current_config = config.copy()
            except Exception as e:
                raise ValueError(f"创建MinIO连接失败: {str(e)}")
        
        return self._client
    
    def _ensure_bucket(self, client, bucket_name: str):
        """确保bucket存在"""
        try:
            if not client.bucket_exists(bucket_name):
                client.make_bucket(bucket_name)
        except Exception as e:
            raise ValueError(f"无法创建或访问bucket {bucket_name}: {str(e)}")
    
    def _generate_object_name(self, original_name: str) -> str:
        """生成对象名（添加时间戳前缀避免重名）
        
        Args:
            original_name: 原始文件名
            
        Returns:
            str: 生成的对象名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{original_name}"
    
    def upload_file(self, file_path: str, object_name: Optional[str] = None, **kwargs) -> StorageResult:
        """上传本地文件到MinIO"""
        try:
            if not os.path.exists(file_path):
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"源文件不存在: {file_path}"
                )
            
            client = self._get_client(**kwargs)
            config = self.config_manager.get_config(**kwargs)
            bucket_name = config['bucket_name']
            
            # 确保bucket存在
            self._ensure_bucket(client, bucket_name)
            
            if object_name is None:
                object_name = os.path.basename(file_path)
            
            # 生成唯一对象名
            unique_object_name = self._generate_object_name(object_name)
            
            # 检测文件类型
            mime_type, _ = mimetypes.guess_type(file_path)
            
            # 上传文件
            result = client.fput_object(
                bucket_name=bucket_name,
                object_name=unique_object_name,
                file_path=file_path,
                content_type=mime_type
            )
            
            # 构建访问URL
            endpoint = config['endpoint']
            secure = config.get('secure', False)
            protocol = 'https' if secure else 'http'
            url = f"{protocol}://{endpoint}/{bucket_name}/{unique_object_name}"
            
            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="文件上传成功",
                data={
                    "object_name": unique_object_name,
                    "etag": result.etag,
                    "url": url,
                    "size": os.path.getsize(file_path)
                }
            )
            
        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"上传文件失败: {str(e)}"
            )
    
    def upload_content(self, content: bytes, object_name: str, content_type: Optional[str] = None, **kwargs) -> StorageResult:
        """上传文件内容到MinIO"""
        try:
            from io import BytesIO
            
            client = self._get_client(**kwargs)
            config = self.config_manager.get_config(**kwargs)
            bucket_name = config['bucket_name']
            
            # 确保bucket存在
            self._ensure_bucket(client, bucket_name)
            
            # 生成唯一对象名
            unique_object_name = self._generate_object_name(object_name)
            
            # 如果没有指定content_type，尝试从文件名推断
            if not content_type:
                content_type, _ = mimetypes.guess_type(object_name)
                if not content_type:
                    content_type = "application/octet-stream"
            
            # 上传内容
            content_stream = BytesIO(content)
            result = client.put_object(
                bucket_name=bucket_name,
                object_name=unique_object_name,
                data=content_stream,
                length=len(content),
                content_type=content_type
            )
            
            # 构建访问URL
            endpoint = config['endpoint']
            secure = config.get('secure', False)
            protocol = 'https' if secure else 'http'
            url = f"{protocol}://{endpoint}/{bucket_name}/{unique_object_name}"
            
            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="内容上传成功",
                data={
                    "object_name": unique_object_name,
                    "etag": result.etag,
                    "url": url,
                    "size": len(content)
                }
            )
            
        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"上传内容失败: {str(e)}"
            )
    
    def list_files(self, prefix: str = "", **kwargs) -> List[FileInfo]:
        """列出MinIO文件"""
        try:
            client = self._get_client(**kwargs)
            config = self.config_manager.get_config(**kwargs)
            bucket_name = config['bucket_name']
            
            files = []
            objects = client.list_objects(bucket_name, prefix=prefix, recursive=True)
            
            for obj in objects:
                file_info = FileInfo(
                    name=obj.object_name,
                    size=obj.size,
                    mime_type="",  # MinIO不直接提供MIME类型
                    created="",
                    modified=obj.last_modified.isoformat() if obj.last_modified else "",
                    object_name=obj.object_name,
                    etag=obj.etag,
                    url=f"{'https' if config.get('secure', False) else 'http'}://{config['endpoint']}/{bucket_name}/{obj.object_name}"
                )
                files.append(file_info)
            
            return files
            
        except Exception as e:
            print(f"列出MinIO文件失败: {str(e)}")
            return []
    
    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除MinIO文件"""
        try:
            client = self._get_client(**kwargs)
            config = self.config_manager.get_config(**kwargs)
            bucket_name = config['bucket_name']
            
            client.remove_object(bucket_name, object_name)
            return True
            
        except Exception as e:
            print(f"删除MinIO文件失败: {str(e)}")
            return False
    
    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> Optional[str]:
        """获取MinIO文件访问URL"""
        try:
            client = self._get_client(**kwargs)
            config = self.config_manager.get_config(**kwargs)
            bucket_name = config['bucket_name']
            
            # 生成预签名URL
            url = client.presigned_get_object(
                bucket_name=bucket_name,
                object_name=object_name,
                expires=timedelta(seconds=expires_in)
            )
            return url
            
        except Exception as e:
            print(f"获取MinIO文件URL失败: {str(e)}")
            return None

    def test_connection(self, **kwargs) -> StorageResult:
        """测试MinIO连接"""
        try:
            if not self.is_available():
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error="MinIO不可用，请安装minio库"
                )

            # 获取配置信息
            config = self.config_manager.get_config(**kwargs)
            is_valid, message = self.config_manager.validate_config(config)

            if not is_valid:
                return StorageResult(
                    success=False,
                    provider=self.provider_name,
                    error=f"配置验证失败: {message}"
                )

            # 尝试连接MinIO
            client = self._get_client(**kwargs)
            bucket_name = config['bucket_name']

            # 检查bucket是否存在，如果不存在则创建
            bucket_exists = client.bucket_exists(bucket_name)
            if not bucket_exists:
                try:
                    client.make_bucket(bucket_name)
                    bucket_created = True
                except Exception as e:
                    return StorageResult(
                        success=False,
                        provider=self.provider_name,
                        error=f"无法创建bucket {bucket_name}: {str(e)}"
                    )
            else:
                bucket_created = False

            # 统计文件数量
            file_count = 0
            total_size = 0
            try:
                objects = client.list_objects(bucket_name, recursive=True)
                for obj in objects:
                    file_count += 1
                    total_size += obj.size
            except Exception:
                file_count = 0
                total_size = 0

            return StorageResult(
                success=True,
                provider=self.provider_name,
                message="MinIO连接测试成功",
                data={
                    "endpoint": config['endpoint'],
                    "bucket_name": bucket_name,
                    "bucket_exists": bucket_exists,
                    "bucket_created": bucket_created,
                    "secure": config.get('secure', False),
                    "file_count": file_count,
                    "total_size": total_size
                }
            )

        except Exception as e:
            return StorageResult(
                success=False,
                provider=self.provider_name,
                error=f"MinIO连接测试失败: {str(e)}"
            )
