#!/usr/bin/env python3
"""
存储服务抽象基类
定义统一的存储接口，支持多种存储方式
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum


class StorageType(Enum):
    """存储类型枚举"""
    LOCAL = "local"
    OSS = "oss"
    MINIO = "minio"


@dataclass
class StorageResult:
    """存储操作结果"""
    success: bool
    provider: str
    message: str = ""
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "success": self.success,
            "provider": self.provider
        }
        
        if self.message:
            result["message"] = self.message
            
        if self.data:
            result.update(self.data)
            
        if self.error:
            result["error"] = self.error
            
        return result


@dataclass
class FileInfo:
    """文件信息"""
    name: str
    size: int
    mime_type: str
    md5: Optional[str] = None
    created: Optional[str] = None
    modified: Optional[str] = None
    url: Optional[str] = None
    object_name: Optional[str] = None
    etag: Optional[str] = None


class BaseStorageProvider(ABC):
    """存储提供商抽象基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化存储提供商
        
        Args:
            config: 存储配置
        """
        self.config = config
        self.provider_name = self.__class__.__name__.replace('Provider', '').lower()
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查存储服务是否可用
        
        Returns:
            bool: 是否可用
        """
        pass
    
    @abstractmethod
    def upload_file(self, file_path: str, object_name: Optional[str] = None, **kwargs) -> StorageResult:
        """上传本地文件
        
        Args:
            file_path: 本地文件路径
            object_name: 存储对象名（可选）
            **kwargs: 额外参数
            
        Returns:
            StorageResult: 上传结果
        """
        pass
    
    @abstractmethod
    def upload_content(self, content: bytes, object_name: str, content_type: Optional[str] = None, **kwargs) -> StorageResult:
        """上传文件内容
        
        Args:
            content: 文件内容
            object_name: 存储对象名
            content_type: 内容类型（可选）
            **kwargs: 额外参数
            
        Returns:
            StorageResult: 上传结果
        """
        pass
    
    @abstractmethod
    def list_files(self, prefix: str = "", **kwargs) -> List[FileInfo]:
        """列出文件
        
        Args:
            prefix: 文件名前缀过滤
            **kwargs: 额外参数
            
        Returns:
            List[FileInfo]: 文件信息列表
        """
        pass
    
    @abstractmethod
    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除文件
        
        Args:
            object_name: 存储对象名
            **kwargs: 额外参数
            
        Returns:
            bool: 是否删除成功
        """
        pass
    
    @abstractmethod
    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> Optional[str]:
        """获取文件访问URL
        
        Args:
            object_name: 存储对象名
            expires_in: URL过期时间（秒）
            **kwargs: 额外参数
            
        Returns:
            Optional[str]: 文件访问URL
        """
        pass
    
    @abstractmethod
    def test_connection(self, **kwargs) -> StorageResult:
        """测试存储连接
        
        Args:
            **kwargs: 额外参数
            
        Returns:
            StorageResult: 测试结果
        """
        pass
    
    def get_provider_info(self) -> Dict[str, Any]:
        """获取存储提供商信息
        
        Returns:
            Dict[str, Any]: 提供商信息
        """
        return {
            "name": self.provider_name,
            "available": self.is_available(),
            "config_keys": list(self.config.keys()) if self.config else []
        }


class BaseConfigManager(ABC):
    """配置管理器抽象基类"""
    
    def __init__(self, storage_type: StorageType):
        """初始化配置管理器
        
        Args:
            storage_type: 存储类型
        """
        self.storage_type = storage_type
        self.config = {}
        self._load_config()
    
    @abstractmethod
    def _load_config(self):
        """加载配置"""
        pass
    
    @abstractmethod
    def get_config(self, **kwargs) -> Dict[str, Any]:
        """获取最终配置
        
        Args:
            **kwargs: 额外配置参数
            
        Returns:
            Dict[str, Any]: 最终配置
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证配置
        
        Args:
            config: 配置字典
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        pass
    
    def get_config_info(self) -> str:
        """获取配置信息（隐藏敏感信息）
        
        Returns:
            str: 配置信息
        """
        safe_config = {}
        for key, value in self.config.items():
            if any(sensitive in key.lower() for sensitive in ['secret', 'key', 'password', 'token']):
                if isinstance(value, str) and len(value) > 8:
                    safe_config[key] = f"{value[:4]}****{value[-4:]}"
                else:
                    safe_config[key] = "****"
            else:
                safe_config[key] = value
        
        import json
        return json.dumps(safe_config, indent=2, ensure_ascii=False)
