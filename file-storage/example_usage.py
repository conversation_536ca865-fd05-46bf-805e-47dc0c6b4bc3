#!/usr/bin/env python3
"""
文件存储服务使用示例
展示如何使用新的多存储架构
"""

import asyncio
import base64
import os
from storage.base import StorageType
from storage.factory import StorageManager


async def example_local_storage():
    """本地存储示例"""
    print("📁 本地存储示例")
    print("=" * 50)
    
    storage_manager = StorageManager()
    
    # 1. 测试连接
    print("1. 测试本地存储连接...")
    result = storage_manager.test_connection(StorageType.LOCAL)
    print(f"   连接状态: {'✅ 成功' if result.success else '❌ 失败'}")
    if result.data:
        print(f"   存储目录: {result.data.get('base_dir')}")
        print(f"   文件数量: {result.data.get('file_count')}")
    
    # 2. 上传文件内容
    print("\n2. 上传文件内容...")
    test_content = "Hello, Local Storage! 这是一个测试文件。"
    content_bytes = test_content.encode('utf-8')
    
    result = storage_manager.upload_content(
        content=content_bytes,
        object_name="example.txt",
        storage_type=StorageType.LOCAL
    )
    
    if result.success:
        print(f"   ✅ 上传成功")
        print(f"   对象名: {result.data.get('object_name')}")
        print(f"   文件大小: {result.data.get('size')} 字节")
        print(f"   MD5: {result.data.get('md5')}")
        uploaded_object = result.data.get('object_name')
    else:
        print(f"   ❌ 上传失败: {result.error}")
        return
    
    # 3. 列出文件
    print("\n3. 列出文件...")
    files = storage_manager.list_files(storage_type=StorageType.LOCAL)
    print(f"   找到 {len(files)} 个文件:")
    for file_info in files[:3]:  # 只显示前3个
        print(f"   - {file_info.name} ({file_info.size} 字节)")
    
    # 4. 获取文件URL
    print("\n4. 获取文件URL...")
    if uploaded_object:
        url = storage_manager.get_file_url(
            object_name=uploaded_object,
            storage_type=StorageType.LOCAL
        )
        print(f"   文件URL: {url}")
    
    # 5. 删除文件
    print("\n5. 删除测试文件...")
    if uploaded_object:
        success = storage_manager.delete_file(
            object_name=uploaded_object,
            storage_type=StorageType.LOCAL
        )
        print(f"   删除结果: {'✅ 成功' if success else '❌ 失败'}")
    
    print("\n✅ 本地存储示例完成\n")


async def example_oss_storage():
    """OSS存储示例"""
    print("☁️ OSS存储示例")
    print("=" * 50)
    
    storage_manager = StorageManager()
    
    # 1. 测试连接
    print("1. 测试OSS连接...")
    result = storage_manager.test_connection(StorageType.OSS)
    print(f"   连接状态: {'✅ 成功' if result.success else '❌ 失败'}")
    if result.success and result.data:
        print(f"   存储桶: {result.data.get('bucket_name')}")
        print(f"   位置: {result.data.get('location')}")
        print(f"   文件数量: {result.data.get('file_count')}")
    elif not result.success:
        print(f"   错误: {result.error}")
        return
    
    # 2. 上传文件内容
    print("\n2. 上传文件内容...")
    test_content = "Hello, OSS Storage! 这是一个测试文件。"
    content_bytes = test_content.encode('utf-8')
    
    result = storage_manager.upload_content(
        content=content_bytes,
        object_name="oss_example.txt",
        storage_type=StorageType.OSS
    )
    
    if result.success:
        print(f"   ✅ 上传成功")
        print(f"   对象名: {result.data.get('object_name')}")
        print(f"   文件大小: {result.data.get('size')} 字节")
        print(f"   ETag: {result.data.get('etag')}")
        uploaded_object = result.data.get('object_name')
    else:
        print(f"   ❌ 上传失败: {result.error}")
        return
    
    # 3. 列出文件
    print("\n3. 列出文件...")
    files = storage_manager.list_files(storage_type=StorageType.OSS)
    print(f"   找到 {len(files)} 个文件:")
    for file_info in files[:3]:  # 只显示前3个
        print(f"   - {file_info.name} ({file_info.size} 字节)")
    
    # 4. 获取文件URL
    print("\n4. 获取文件URL...")
    if uploaded_object:
        url = storage_manager.get_file_url(
            object_name=uploaded_object,
            expires_in=3600,
            storage_type=StorageType.OSS
        )
        if url:
            print(f"   文件URL: {url[:80]}...")
        else:
            print("   ❌ 无法获取URL")
    
    # 5. 删除文件
    print("\n5. 删除测试文件...")
    if uploaded_object:
        success = storage_manager.delete_file(
            object_name=uploaded_object,
            storage_type=StorageType.OSS
        )
        print(f"   删除结果: {'✅ 成功' if success else '❌ 失败'}")
    
    print("\n✅ OSS存储示例完成\n")


async def example_storage_info():
    """存储信息示例"""
    print("ℹ️ 存储信息示例")
    print("=" * 50)
    
    storage_manager = StorageManager()
    
    # 获取存储信息
    storage_info = storage_manager.get_storage_info()
    
    print(f"默认存储类型: {storage_info['default_storage_type'].upper()}")
    print("\n可用的存储提供商:")
    
    for provider in storage_info['available_providers']:
        status = '✅ 可用' if provider['available'] else '❌ 不可用'
        print(f"  - {provider['name']}: {status}")
        
        if not provider['available'] and 'error' in provider:
            print(f"    错误: {provider['error']}")
    
    print("\n✅ 存储信息示例完成\n")


async def example_base64_upload():
    """Base64上传示例"""
    print("📤 Base64上传示例")
    print("=" * 50)
    
    storage_manager = StorageManager()
    
    # 创建一个简单的文本文件内容
    text_content = "这是一个Base64编码的文件示例\nHello, World!\n你好，世界！"
    content_bytes = text_content.encode('utf-8')
    content_base64 = base64.b64encode(content_bytes).decode('utf-8')
    
    print(f"原始内容长度: {len(content_bytes)} 字节")
    print(f"Base64编码长度: {len(content_base64)} 字符")
    
    # 上传到本地存储
    print("\n上传到本地存储...")
    result = storage_manager.upload_content(
        content=content_bytes,
        object_name="base64_example.txt",
        content_type="text/plain",
        storage_type=StorageType.LOCAL
    )
    
    if result.success:
        print(f"   ✅ 上传成功: {result.data.get('object_name')}")
        
        # 清理
        storage_manager.delete_file(
            object_name=result.data.get('object_name'),
            storage_type=StorageType.LOCAL
        )
    else:
        print(f"   ❌ 上传失败: {result.error}")
    
    print("\n✅ Base64上传示例完成\n")


async def main():
    """主函数"""
    print("🚀 文件存储服务使用示例")
    print("=" * 60)
    print()
    
    # 运行各种示例
    await example_storage_info()
    await example_local_storage()
    await example_oss_storage()
    await example_base64_upload()
    
    print("🎉 所有示例完成！")
    print("\n💡 提示:")
    print("- 本地存储适合开发和测试")
    print("- OSS存储适合生产环境")
    print("- 可以通过环境变量或配置文件自定义配置")
    print("- 支持多种文件格式和大文件上传")


if __name__ == "__main__":
    asyncio.run(main())
