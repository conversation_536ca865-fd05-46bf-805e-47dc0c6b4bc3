#!/usr/bin/env python3
"""
文件存储 MCP 工具 - 重构版本
支持多种存储方式：本地存储、阿里云OSS、MinIO等
"""

import os
import sys
import base64
import hashlib
import mimetypes
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp.server.fastmcp import FastMCP

# 导入存储相关模块
from storage.base import StorageType
from storage.factory import StorageManager
from storage.config import LocalConfigManager, OSSConfigManager, MinIOConfigManager

# 初始化 FastMCP server，配置为无状态HTTP模式以支持多协议
mcp = FastMCP("file-storage", stateless_http=True)

# 创建统一存储管理器
storage_manager = StorageManager()

# 配置本地上传目录（兼容性）
UPLOAD_DIR = os.path.join(os.path.dirname(__file__), "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)


def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件信息"""
    if not os.path.exists(file_path):
        return {"error": "文件不存在"}

    stat = os.stat(file_path)
    mime_type, _ = mimetypes.guess_type(file_path)

    # 计算文件哈希
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)

    return {
        "name": os.path.basename(file_path),
        "size": stat.st_size,
        "mime_type": mime_type or "application/octet-stream",
        "md5": hash_md5.hexdigest(),
        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
        "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
    }


def parse_storage_type(storage_type_str: Optional[str]) -> Optional[StorageType]:
    """解析存储类型字符串
    
    Args:
        storage_type_str: 存储类型字符串
        
    Returns:
        Optional[StorageType]: 存储类型枚举
    """
    if not storage_type_str:
        return None
    
    try:
        return StorageType(storage_type_str.lower())
    except ValueError:
        return None


@mcp.tool()
async def upload_file_content(
    file_content_base64: str,
    filename: str,
    storage_type: str = "oss",
    expires_in: int = 3600
) -> str:
    """上传文件内容到指定存储

    Args:
        file_content_base64: Base64编码的文件内容
        filename: 文件名
        storage_type: 存储类型 (local/oss/minio)
        expires_in: URL有效期（秒，默认3600秒即1小时）

    Returns:
        上传结果信息
    """
    try:
        # 解析存储类型
        storage_type_enum = parse_storage_type(storage_type)
        if storage_type_enum is None:
            return f"❌ 不支持的存储类型: {storage_type}，支持的类型: local, oss, minio"
        
        # 解码文件内容
        try:
            file_content = base64.b64decode(file_content_base64)
        except Exception as e:
            return f"❌ Base64解码失败: {str(e)}"
        
        # 检测文件类型
        mime_type, _ = mimetypes.guess_type(filename)
        if not mime_type:
            mime_type = "application/octet-stream"
        
        # 上传文件
        result = storage_manager.upload_content(
            content=file_content,
            object_name=filename,
            content_type=mime_type,
            storage_type=storage_type_enum,
            expires_in=expires_in,
            **config
        )

        if result.success:
            expires_time = datetime.now() + timedelta(seconds=expires_in)
            return f"""✅ 文件上传成功！

📄 文件信息:
- 文件名: {filename}
- 大小: {result.data.get('size', 0):,} 字节
- 存储提供商: {result.provider.upper()}
- 对象名: {result.data.get('object_name', filename)}

🔗 访问链接:
{result.data.get('url', '未生成')}

⏰ 链接有效期:
- 有效时长: {expires_in} 秒 ({expires_in//3600}小时{(expires_in%3600)//60}分钟)
- 过期时间: {expires_time.strftime('%Y-%m-%d %H:%M:%S')}

📥 文件已成功上传到{result.provider.upper()}存储。
"""
        else:
            return f"❌ 上传失败: {result.error}"
    
    except Exception as e:
        return f"❌ 上传过程中发生错误: {str(e)}"


@mcp.tool()
async def upload_local_file(
    file_path: str,
    object_name: Optional[str] = None,
    storage_type: str = "oss",
    expires_in: int = 3600,
    **config
) -> str:
    """上传本地文件到指定存储

    Args:
        file_path: 本地文件路径
        object_name: 存储对象名（可选）
        storage_type: 存储类型 (local/oss/minio)
        expires_in: URL有效期（秒，默认3600秒即1小时）
        **config: 存储配置参数

    Returns:
        上传结果信息
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return f"❌ 文件不存在: {file_path}"
        
        # 解析存储类型
        storage_type_enum = parse_storage_type(storage_type)
        if storage_type_enum is None:
            return f"❌ 不支持的存储类型: {storage_type}，支持的类型: local, oss, minio"
        
        # 获取文件信息
        file_info = get_file_info(file_path)
        if "error" in file_info:
            return f"❌ {file_info['error']}"
        
        # 上传文件
        result = storage_manager.upload_file(
            file_path=file_path,
            object_name=object_name,
            storage_type=storage_type_enum,
            expires_in=expires_in,
            **config
        )

        if result.success:
            expires_time = datetime.now() + timedelta(seconds=expires_in)
            return f"""✅ 文件上传成功！

📄 文件信息:
- 原始文件: {file_info['name']}
- 大小: {file_info['size']:,} 字节
- 类型: {file_info['mime_type']}
- MD5: {file_info['md5']}
- 存储提供商: {result.provider.upper()}
- 对象名: {result.data.get('object_name', object_name or file_info['name'])}

🔗 访问链接:
{result.data.get('url', '未生成')}

⏰ 链接有效期:
- 有效时长: {expires_in} 秒 ({expires_in//3600}小时{(expires_in%3600)//60}分钟)
- 过期时间: {expires_time.strftime('%Y-%m-%d %H:%M:%S')}

📥 文件已成功上传到{result.provider.upper()}存储。
"""
        else:
            return f"❌ 上传失败: {result.error}"
    
    except Exception as e:
        return f"❌ 上传过程中发生错误: {str(e)}"


@mcp.tool()
async def list_files(
    prefix: str = "",
    storage_type: str = "oss",
    expires_in: int = 3600,
    **config
) -> str:
    """列出指定存储中的文件

    Args:
        prefix: 文件名前缀过滤
        storage_type: 存储类型 (local/oss/minio)
        expires_in: URL有效期（秒，默认3600秒即1小时）
        **config: 存储配置参数

    Returns:
        文件列表信息
    """
    try:
        # 解析存储类型
        storage_type_enum = parse_storage_type(storage_type)
        if storage_type_enum is None:
            return f"❌ 不支持的存储类型: {storage_type}，支持的类型: local, oss, minio"
        
        # 获取文件列表
        files = storage_manager.list_files(
            prefix=prefix,
            storage_type=storage_type_enum,
            expires_in=expires_in,
            **config
        )

        if not files:
            return f"📁 {storage_type.upper()}存储中没有找到文件"

        # 格式化文件列表
        expires_time = datetime.now() + timedelta(seconds=expires_in)
        result = f"📁 {storage_type.upper()}存储文件列表:\n\n"
        total_size = 0

        for file_info in files:
            size = file_info.size
            total_size += size

            result += f"📄 {file_info.name}\n"
            result += f"   大小: {size:,} 字节\n"
            if file_info.modified:
                result += f"   修改时间: {file_info.modified}\n"
            if file_info.url:
                result += f"   访问链接: {file_info.url}\n"
                if hasattr(file_info, 'expires_in') and file_info.expires_in:
                    result += f"   链接有效期: {file_info.expires_in} 秒 ({file_info.expires_in//3600}小时{(file_info.expires_in%3600)//60}分钟)\n"
                    result += f"   过期时间: {expires_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            result += "\n"

        result += f"📊 总计: {len(files)} 个文件，{total_size:,} 字节\n"
        result += f"⏰ 所有链接有效期: {expires_in} 秒 ({expires_in//3600}小时{(expires_in%3600)//60}分钟)"
        return result
    
    except Exception as e:
        return f"❌ 获取文件列表失败: {str(e)}"


@mcp.tool()
async def delete_file(
    object_name: str,
    storage_type: str = "oss",
    **config
) -> str:
    """删除指定存储中的文件
    
    Args:
        object_name: 要删除的对象名
        storage_type: 存储类型 (local/oss/minio)
        **config: 存储配置参数
        
    Returns:
        删除结果信息
    """
    try:
        # 解析存储类型
        storage_type_enum = parse_storage_type(storage_type)
        if storage_type_enum is None:
            return f"❌ 不支持的存储类型: {storage_type}，支持的类型: local, oss, minio"
        
        # 删除文件
        success = storage_manager.delete_file(
            object_name=object_name,
            storage_type=storage_type_enum,
            **config
        )
        
        if success:
            return f"""✅ 文件删除成功！
            
📄 已删除文件:
- 对象名: {object_name}
- 存储提供商: {storage_type.upper()}

🗑️ 文件已从{storage_type.upper()}存储中永久删除。
"""
        else:
            return f"❌ 删除失败: 文件可能不存在或没有权限"
    
    except Exception as e:
        return f"❌ 删除过程中发生错误: {str(e)}"


@mcp.tool()
async def get_file_url(
    object_name: str,
    expires_in: int = 3600,
    storage_type: str = "oss",
    **config
) -> str:
    """获取指定存储中文件的访问URL

    Args:
        object_name: 对象名
        expires_in: URL过期时间（秒）
        storage_type: 存储类型 (local/oss/minio)
        **config: 存储配置参数

    Returns:
        文件访问URL信息
    """
    try:
        # 解析存储类型
        storage_type_enum = parse_storage_type(storage_type)
        if storage_type_enum is None:
            return f"❌ 不支持的存储类型: {storage_type}，支持的类型: local, oss, minio"

        # 获取文件URL
        url = storage_manager.get_file_url(
            object_name=object_name,
            expires_in=expires_in,
            storage_type=storage_type_enum,
            **config
        )

        if url:
            expires_time = datetime.now() + timedelta(seconds=expires_in)
            return f"""✅ 文件URL生成成功！

📄 文件信息:
- 对象名: {object_name}
- 存储提供商: {storage_type.upper()}

🔗 访问链接:
{url}

⏰ 链接有效期:
- 有效时长: {expires_in} 秒 ({expires_in//3600}小时{(expires_in%3600)//60}分钟)
- 过期时间: {expires_time.strftime('%Y-%m-%d %H:%M:%S')}

💡 请在有效期内使用此链接访问文件。
"""
        else:
            return f"❌ 无法生成访问URL: 文件可能不存在或配置有误"

    except Exception as e:
        return f"❌ 获取URL过程中发生错误: {str(e)}"


@mcp.tool()
async def test_storage_connection(
    storage_type: str = "oss",
    **config
) -> str:
    """测试指定存储的连接配置

    Args:
        storage_type: 存储类型 (local/oss/minio)
        **config: 存储配置参数

    Returns:
        连接测试结果和配置信息
    """
    try:
        # 解析存储类型
        storage_type_enum = parse_storage_type(storage_type)
        if storage_type_enum is None:
            return f"❌ 不支持的存储类型: {storage_type}，支持的类型: local, oss, minio"

        # 测试连接
        result = storage_manager.test_connection(
            storage_type=storage_type_enum,
            **config
        )

        if result.success:
            data_info = ""
            if result.data:
                for key, value in result.data.items():
                    data_info += f"- {key}: {value}\n"

            return f"""✅ {storage_type.upper()}连接测试成功！

📊 连接状态:
- 状态: ✅ 正常
- 存储类型: {storage_type.upper()}
{data_info}
💡 存储服务运行正常，可以正常使用。
"""
        else:
            return f"""❌ {storage_type.upper()}连接测试失败

❌ 错误详情:
{result.error}

🔧 可能的解决方案:
1. 检查配置参数是否正确
2. 确认网络连接是否正常
3. 验证访问权限是否足够
4. 检查存储服务是否正常运行
"""

    except Exception as e:
        return f"❌ 测试过程中发生错误: {str(e)}"


@mcp.tool()
async def get_storage_info() -> str:
    """获取文件存储工具的信息和使用说明

    Returns:
        工具信息和使用说明
    """
    try:
        storage_info = storage_manager.get_storage_info()
        available_providers = storage_info['available_providers']

        result = """📚 多存储文件存储工具

🔧 支持的存储服务:"""

        for provider in available_providers:
            status = '✅ 可用' if provider['available'] else '❌ 不可用'
            result += f"\n- {provider['name']}: {status}"
            if not provider['available'] and 'error' in provider:
                result += f" ({provider['error']})"

        result += f"""

✨ 主要功能:
- 文件上传 (Base64内容或本地文件)
- 文件列表查看
- 文件删除
- 生成访问URL
- 自动文件类型检测
- 存储连接测试
- 多存储方式支持

🎯 默认存储类型: {storage_info['default_storage_type'].upper()}

📁 本地上传目录: {UPLOAD_DIR}

💡 使用提示:
1. 支持本地存储、阿里云OSS、MinIO等多种存储方式
2. 可通过storage_type参数指定存储类型
3. 每种存储方式都有独立的配置管理
4. 文件上传时会自动添加时间戳前缀避免重名
5. 支持多种文件格式的MIME类型检测
6. 生成的URL有时效性（对象存储）
7. 使用 test_storage_connection 工具测试连接

🔧 存储类型:
- local: 本地文件存储
- oss: 阿里云对象存储
- minio: MinIO对象存储

📋 配置优先级 (从高到低):
1. 函数参数配置 (调用时传入)
2. 环境变量配置
3. 配置文件
4. 默认配置

🛠️ 依赖安装:
- OSS: pip install oss2
- MinIO: pip install minio
"""

        return result

    except Exception as e:
        return f"❌ 获取存储信息失败: {str(e)}"


@mcp.resource("file-storage://info")
def get_storage_resource_info() -> str:
    """获取存储信息资源"""
    try:
        storage_info = storage_manager.get_storage_info()
        available_providers = storage_info['available_providers']

        result = f"""文件存储服务信息:

默认存储类型: {storage_info['default_storage_type'].upper()}

支持的存储服务:"""

        for provider in available_providers:
            status = "✅ 可用" if provider['available'] else "❌ 不可用"
            result += f"\n- {provider['name']}: {status}"

        result += """

🔧 配置优先级 (从高到低):
1. 函数参数配置 (调用时传入)
2. 环境变量配置
3. 配置文件
4. 默认配置

📋 环境变量配置示例:
# 本地存储
export LOCAL_STORAGE_BASE_DIR="/path/to/storage"
export LOCAL_STORAGE_MAX_FILE_SIZE="104857600"

# 阿里云OSS
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-shenzhen.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"

# MinIO
export MINIO_ENDPOINT="localhost:9000"
export MINIO_ACCESS_KEY="minioadmin"
export MINIO_SECRET_KEY="minioadmin"
export MINIO_BUCKET_NAME="default"
export MINIO_SECURE="false"

🔧 工具功能:
- upload_file_content: 上传Base64编码的文件内容
- upload_local_file: 上传本地文件
- list_files: 列出文件
- delete_file: 删除文件
- get_file_url: 获取文件访问URL
- test_storage_connection: 测试存储连接
- get_storage_info: 获取存储信息
"""
        return result

    except Exception as e:
        return f"获取存储信息失败: {str(e)}"


@mcp.resource("file-storage://config-template")
def get_config_template() -> str:
    """获取配置模板资源"""
    return """文件存储配置模板:

# 方式1: 环境变量配置（推荐）

## 本地存储配置
export LOCAL_STORAGE_BASE_DIR="/path/to/storage"
export LOCAL_STORAGE_CREATE_SUBDIRS="true"
export LOCAL_STORAGE_MAX_FILE_SIZE="104857600"  # 100MB
export LOCAL_STORAGE_ALLOWED_EXTENSIONS=".jpg,.png,.pdf,.txt"

## 阿里云OSS配置
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-shenzhen.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"

## MinIO配置
export MINIO_ENDPOINT="localhost:9000"
export MINIO_ACCESS_KEY="minioadmin"
export MINIO_SECRET_KEY="minioadmin"
export MINIO_BUCKET_NAME="default"
export MINIO_SECURE="false"

# 方式2: 配置文件

## 本地存储配置文件 (local_config.json)
{
  "base_dir": "/path/to/storage",
  "create_subdirs": true,
  "max_file_size": 104857600,
  "allowed_extensions": [".jpg", ".png", ".pdf", ".txt"]
}

## OSS配置文件 (oss_config.json)
{
  "access_key_id": "your-access-key-id",
  "access_key_secret": "your-access-key-secret",
  "endpoint": "https://oss-cn-shenzhen.aliyuncs.com",
  "bucket_name": "your-bucket-name"
}

## MinIO配置文件 (minio_config.json)
{
  "endpoint": "localhost:9000",
  "access_key": "minioadmin",
  "secret_key": "minioadmin",
  "bucket_name": "default",
  "secure": false
}

# 方式3: 函数参数配置
await upload_file_content(
    file_content_base64="...",
    filename="test.jpg",
    storage_type="oss",
    access_key_id="your-access-key-id",
    access_key_secret="your-access-key-secret",
    endpoint="https://oss-cn-shenzhen.aliyuncs.com",
    bucket_name="your-bucket-name"
)

💡 使用建议:
1. 开发测试: 使用本地存储，简单快速
2. 生产环境: 使用环境变量配置，更安全
3. 个人使用: 可使用配置文件，方便管理
4. 临时使用: 通过函数参数传递配置

注意事项:
1. 确保存储服务已正确配置并可访问
2. 妥善保管访问密钥，不要泄露
3. 配置文件不要提交到版本控制系统
4. 选择合适的存储类型以满足性能和成本需求
"""


@mcp.tool()
async def run_storage_tests(
    storage_type: str = "all",
    cleanup: bool = True
) -> str:
    """运行存储服务测试

    Args:
        storage_type: 要测试的存储类型 (all/local/oss/minio)
        cleanup: 是否清理测试文件

    Returns:
        测试结果报告
    """
    try:
        results = []

        # 解析存储类型
        if storage_type.lower() == "all":
            test_types = ["local", "oss", "minio"]
        else:
            test_types = [storage_type.lower()]

        results.append("🚀 开始存储服务测试\n")

        # 首先测试存储信息
        results.append("🧪 测试存储信息获取...")
        try:
            storage_info = storage_manager.get_storage_info()
            results.append(f"默认存储类型: {storage_info['default_storage_type']}")
            results.append("可用存储提供商:")

            for provider in storage_info['available_providers']:
                status = '✅ 可用' if provider['available'] else '❌ 不可用'
                results.append(f"  - {provider['name']}: {status}")
                if not provider['available'] and 'error' in provider:
                    results.append(f"    错误: {provider['error']}")

            results.append("✅ 存储信息获取测试完成\n")
        except Exception as e:
            results.append(f"❌ 存储信息测试失败: {str(e)}\n")

        # 测试各种存储类型
        for test_type in test_types:
            if test_type == "local":
                result = await _test_local_storage(cleanup)
                results.append(result)
            elif test_type == "oss":
                result = await _test_oss_storage(cleanup)
                results.append(result)
            elif test_type == "minio":
                result = await _test_minio_storage(cleanup)
                results.append(result)
            else:
                results.append(f"❌ 不支持的存储类型: {test_type}")

        results.append("🎉 所有测试完成！")

        return "\n".join(results)

    except Exception as e:
        return f"❌ 测试过程中发生错误: {str(e)}"


async def _test_local_storage(cleanup: bool = True) -> str:
    """测试本地存储"""
    results = []
    results.append("🧪 测试本地存储...")

    try:
        # 1. 测试连接
        results.append("1. 测试连接...")
        test_result = storage_manager.test_connection(StorageType.LOCAL)
        if test_result.success:
            results.append("   连接测试: ✅ 成功")
            if test_result.data:
                results.append(f"   存储目录: {test_result.data.get('base_dir')}")
                results.append(f"   文件数量: {test_result.data.get('file_count')}")
        else:
            results.append("   连接测试: ❌ 失败")
            results.append(f"   错误: {test_result.error}")
            return "\n".join(results)

        # 2. 测试上传内容
        results.append("2. 测试上传内容...")
        test_content = b"Hello, World! This is a test file."
        upload_result = storage_manager.upload_content(
            content=test_content,
            object_name="test.txt",
            storage_type=StorageType.LOCAL
        )

        if upload_result.success:
            results.append("   上传内容: ✅ 成功")
            uploaded_object_name = upload_result.data.get('object_name')
            results.append(f"   对象名: {uploaded_object_name}")
            results.append(f"   文件大小: {upload_result.data.get('size')} 字节")
        else:
            results.append("   上传内容: ❌ 失败")
            results.append(f"   错误: {upload_result.error}")
            return "\n".join(results)

        # 3. 测试列出文件
        results.append("3. 测试列出文件...")
        files = storage_manager.list_files(storage_type=StorageType.LOCAL)
        results.append(f"   找到文件数量: {len(files)}")

        # 4. 测试获取文件URL
        results.append("4. 测试获取文件URL...")
        if uploaded_object_name:
            url = storage_manager.get_file_url(
                object_name=uploaded_object_name,
                storage_type=StorageType.LOCAL
            )
            if url:
                results.append(f"   文件URL: {url}")
            else:
                results.append("   文件URL: ❌ 获取失败")

        # 5. 测试删除文件
        if cleanup and uploaded_object_name:
            results.append("5. 测试删除文件...")
            success = storage_manager.delete_file(
                object_name=uploaded_object_name,
                storage_type=StorageType.LOCAL
            )
            results.append(f"   删除文件: {'✅ 成功' if success else '❌ 失败'}")

        results.append("✅ 本地存储测试完成\n")

    except Exception as e:
        results.append(f"❌ 本地存储测试失败: {str(e)}\n")

    return "\n".join(results)


async def _test_oss_storage(cleanup: bool = True) -> str:
    """测试OSS存储"""
    results = []
    results.append("🧪 测试OSS存储...")

    try:
        # 1. 测试连接
        results.append("1. 测试连接...")
        test_result = storage_manager.test_connection(StorageType.OSS)
        if test_result.success:
            results.append("   连接测试: ✅ 成功")
            if test_result.data:
                results.append(f"   存储桶: {test_result.data.get('bucket_name')}")
                results.append(f"   位置: {test_result.data.get('location')}")
                results.append(f"   文件数量: {test_result.data.get('file_count')}")
        else:
            results.append("   连接测试: ❌ 失败")
            results.append(f"   错误: {test_result.error}")
            return "\n".join(results)

        # 2. 测试上传内容
        results.append("2. 测试上传内容...")
        test_content = b"Hello, OSS! This is a test file."
        upload_result = storage_manager.upload_content(
            content=test_content,
            object_name="test_oss.txt",
            storage_type=StorageType.OSS
        )

        if upload_result.success:
            results.append("   上传内容: ✅ 成功")
            uploaded_object_name = upload_result.data.get('object_name')
            results.append(f"   对象名: {uploaded_object_name}")
            results.append(f"   文件大小: {upload_result.data.get('size')} 字节")
            results.append(f"   ETag: {upload_result.data.get('etag')}")
        else:
            results.append("   上传内容: ❌ 失败")
            results.append(f"   错误: {upload_result.error}")
            return "\n".join(results)

        # 3. 测试列出文件
        results.append("3. 测试列出文件...")
        files = storage_manager.list_files(storage_type=StorageType.OSS)
        results.append(f"   找到文件数量: {len(files)}")

        # 4. 测试获取文件URL
        results.append("4. 测试获取文件URL...")
        if uploaded_object_name:
            url = storage_manager.get_file_url(
                object_name=uploaded_object_name,
                storage_type=StorageType.OSS
            )
            if url:
                url_display = url[:80] + "..." if len(url) > 80 else url
                results.append(f"   文件URL: {url_display}")
            else:
                results.append("   文件URL: ❌ 获取失败")

        # 5. 测试删除文件
        if cleanup and uploaded_object_name:
            results.append("5. 测试删除文件...")
            success = storage_manager.delete_file(
                object_name=uploaded_object_name,
                storage_type=StorageType.OSS
            )
            results.append(f"   删除文件: {'✅ 成功' if success else '❌ 失败'}")

        results.append("✅ OSS存储测试完成\n")

    except Exception as e:
        results.append(f"❌ OSS存储测试失败: {str(e)}\n")

    return "\n".join(results)


async def _test_minio_storage(cleanup: bool = True) -> str:
    """测试MinIO存储"""
    results = []
    results.append("🧪 测试MinIO存储...")

    try:
        # 1. 测试连接
        results.append("1. 测试连接...")
        test_result = storage_manager.test_connection(StorageType.MINIO)
        if test_result.success:
            results.append("   连接测试: ✅ 成功")
            if test_result.data:
                results.append(f"   端点: {test_result.data.get('endpoint')}")
                results.append(f"   存储桶: {test_result.data.get('bucket_name')}")
                results.append(f"   文件数量: {test_result.data.get('file_count')}")
        else:
            results.append("   连接测试: ❌ 失败")
            results.append(f"   错误: {test_result.error}")
            return "\n".join(results)

        # 2. 测试上传内容
        results.append("2. 测试上传内容...")
        test_content = b"Hello, MinIO! This is a test file."
        upload_result = storage_manager.upload_content(
            content=test_content,
            object_name="test_minio.txt",
            storage_type=StorageType.MINIO
        )

        if upload_result.success:
            results.append("   上传内容: ✅ 成功")
            uploaded_object_name = upload_result.data.get('object_name')
            results.append(f"   对象名: {uploaded_object_name}")
            results.append(f"   文件大小: {upload_result.data.get('size')} 字节")
            results.append(f"   ETag: {upload_result.data.get('etag')}")
        else:
            results.append("   上传内容: ❌ 失败")
            results.append(f"   错误: {upload_result.error}")
            return "\n".join(results)

        # 3. 测试列出文件
        results.append("3. 测试列出文件...")
        files = storage_manager.list_files(storage_type=StorageType.MINIO)
        results.append(f"   找到文件数量: {len(files)}")

        # 4. 测试获取文件URL
        results.append("4. 测试获取文件URL...")
        if uploaded_object_name:
            url = storage_manager.get_file_url(
                object_name=uploaded_object_name,
                storage_type=StorageType.MINIO
            )
            if url:
                url_display = url[:80] + "..." if len(url) > 80 else url
                results.append(f"   文件URL: {url_display}")
            else:
                results.append("   文件URL: ❌ 获取失败")

        # 5. 测试删除文件
        if cleanup and uploaded_object_name:
            results.append("5. 测试删除文件...")
            success = storage_manager.delete_file(
                object_name=uploaded_object_name,
                storage_type=StorageType.MINIO
            )
            results.append(f"   删除文件: {'✅ 成功' if success else '❌ 失败'}")

        results.append("✅ MinIO存储测试完成\n")

    except Exception as e:
        results.append(f"❌ MinIO存储测试失败: {str(e)}\n")

    return "\n".join(results)


def main():
    """主函数，启动MCP服务器，使用Streamable HTTP协议"""
    mcp.run(transport='streamable-http')


if __name__ == "__main__":
    main()
