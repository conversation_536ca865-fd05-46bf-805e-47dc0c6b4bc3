#!/usr/bin/env python3
"""
测试本地存储安全性
验证本地存储是否固定在uploads目录下，并防止路径遍历攻击
"""

import os
import sys
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from storage.base import StorageType
from storage.factory import StorageManager
from storage.local import LocalStorageProvider
from storage.config import LocalConfigManager

def test_local_storage_security():
    """测试本地存储安全性"""
    print("🧪 开始测试本地存储安全性...")
    
    # 创建存储管理器
    storage_manager = StorageManager()
    
    # 获取uploads目录路径
    uploads_dir = os.path.join(os.path.dirname(__file__), "uploads")
    print(f"📁 预期uploads目录: {uploads_dir}")
    
    try:
        # 1. 测试配置固定性
        print("\n1️⃣ 测试配置固定性...")
        
        # 创建本地存储提供商
        local_provider = LocalStorageProvider()
        config = local_provider.config_manager.get_config()
        actual_base_dir = config.get('base_dir')
        
        print(f"   实际base_dir: {actual_base_dir}")
        print(f"   预期base_dir: {uploads_dir}")
        
        if os.path.abspath(actual_base_dir) == os.path.abspath(uploads_dir):
            print("   ✅ base_dir固定为uploads目录")
        else:
            print("   ❌ base_dir未固定为uploads目录")
        
        # 2. 测试外部配置参数无效
        print("\n2️⃣ 测试外部配置参数无效...")
        
        # 尝试通过参数修改base_dir
        malicious_config = local_provider.config_manager.get_config(base_dir="/tmp/malicious")
        actual_base_dir_2 = malicious_config.get('base_dir')
        
        print(f"   尝试设置恶意base_dir: /tmp/malicious")
        print(f"   实际获得base_dir: {actual_base_dir_2}")
        
        if os.path.abspath(actual_base_dir_2) == os.path.abspath(uploads_dir):
            print("   ✅ 外部配置参数被忽略，base_dir仍为uploads目录")
        else:
            print("   ❌ 外部配置参数生效，存在安全风险")
        
        # 3. 测试路径遍历攻击防护
        print("\n3️⃣ 测试路径遍历攻击防护...")
        
        # 测试各种恶意路径
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/passwd",
            "\\windows\\system32\\config\\sam",
            "../../malicious.txt",
            "../uploads/../../../etc/passwd"
        ]
        
        test_content = b"This should not be written outside uploads directory"
        
        for malicious_path in malicious_paths:
            print(f"   测试恶意路径: {malicious_path}")
            
            try:
                result = storage_manager.upload_content(
                    content=test_content,
                    object_name=malicious_path,
                    storage_type=StorageType.LOCAL
                )
                
                if result.success:
                    print(f"     ❌ 恶意路径上传成功，存在安全风险")
                    # 清理可能创建的文件
                    if result.data and result.data.get('object_name'):
                        storage_manager.delete_file(
                            object_name=result.data['object_name'],
                            storage_type=StorageType.LOCAL
                        )
                else:
                    print(f"     ✅ 恶意路径被阻止: {result.error}")
            except Exception as e:
                print(f"     ✅ 恶意路径被阻止: {str(e)}")
        
        # 4. 测试正常文件上传
        print("\n4️⃣ 测试正常文件上传...")
        
        normal_filename = "test_normal.txt"
        normal_content = b"This is a normal test file"
        
        result = storage_manager.upload_content(
            content=normal_content,
            object_name=normal_filename,
            storage_type=StorageType.LOCAL
        )
        
        if result.success:
            print("   ✅ 正常文件上传成功")
            uploaded_object_name = result.data.get('object_name')
            uploaded_url = result.data.get('url')
            
            print(f"   📄 对象名: {uploaded_object_name}")
            print(f"   🔗 URL: {uploaded_url}")
            
            # 验证文件确实在uploads目录内
            if uploaded_url and uploaded_url.startswith('file://'):
                file_path = uploaded_url[7:]  # 移除 'file://' 前缀
                abs_file_path = os.path.abspath(file_path)
                abs_uploads_dir = os.path.abspath(uploads_dir)
                
                if abs_file_path.startswith(abs_uploads_dir):
                    print("   ✅ 文件确实保存在uploads目录内")
                else:
                    print(f"   ❌ 文件保存在uploads目录外: {abs_file_path}")
            
            # 5. 测试文件列表
            print("\n5️⃣ 测试文件列表...")
            
            files = storage_manager.list_files(
                storage_type=StorageType.LOCAL
            )
            
            found_uploaded_file = False
            for file_info in files:
                if file_info.object_name == uploaded_object_name:
                    found_uploaded_file = True
                    print(f"   ✅ 找到上传的文件: {file_info.object_name}")
                    
                    # 验证文件URL也在uploads目录内
                    if file_info.url and file_info.url.startswith('file://'):
                        file_path = file_info.url[7:]
                        abs_file_path = os.path.abspath(file_path)
                        abs_uploads_dir = os.path.abspath(uploads_dir)
                        
                        if abs_file_path.startswith(abs_uploads_dir):
                            print("   ✅ 列表中的文件URL也在uploads目录内")
                        else:
                            print(f"   ❌ 列表中的文件URL在uploads目录外: {abs_file_path}")
                    break
            
            if not found_uploaded_file:
                print("   ❌ 未在文件列表中找到上传的文件")
            
            # 6. 测试文件删除
            print("\n6️⃣ 测试文件删除...")
            
            # 测试恶意删除路径
            malicious_delete_paths = [
                "../../../important.txt",
                "/etc/passwd",
                "../../config.json"
            ]
            
            for malicious_path in malicious_delete_paths:
                print(f"   测试恶意删除路径: {malicious_path}")
                success = storage_manager.delete_file(
                    object_name=malicious_path,
                    storage_type=StorageType.LOCAL
                )
                
                if success:
                    print(f"     ❌ 恶意删除成功，存在安全风险")
                else:
                    print(f"     ✅ 恶意删除被阻止")
            
            # 删除正常上传的测试文件
            delete_success = storage_manager.delete_file(
                object_name=uploaded_object_name,
                storage_type=StorageType.LOCAL
            )
            
            if delete_success:
                print("   ✅ 正常文件删除成功")
            else:
                print("   ❌ 正常文件删除失败")
        
        else:
            print(f"   ❌ 正常文件上传失败: {result.error}")
        
        # 7. 测试连接测试
        print("\n7️⃣ 测试连接测试...")
        
        test_result = storage_manager.test_connection(StorageType.LOCAL)
        
        if test_result.success:
            print("   ✅ 连接测试成功")
            if test_result.data:
                reported_base_dir = test_result.data.get('base_dir')
                print(f"   📁 报告的base_dir: {reported_base_dir}")
                
                if os.path.abspath(reported_base_dir) == os.path.abspath(uploads_dir):
                    print("   ✅ 连接测试报告的base_dir正确")
                else:
                    print("   ❌ 连接测试报告的base_dir不正确")
        else:
            print(f"   ❌ 连接测试失败: {test_result.error}")
        
        print("\n🎉 本地存储安全性测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_local_storage_security()
