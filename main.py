#!/usr/bin/env python3
"""
Coli AI MCP Server - 多MCP工具统一管理服务器
支持多个MCP工具的统一部署和管理，使用多种协议
"""

import contextlib
import uvicorn
import os
import json
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse

# 导入MCP工具注册器
from mcp_registry import registry


# 自动注册所有MCP工具
print("🚀 启动Coli AI MCP Server")
registry.register_all_tools()

# 获取注册的工具
registered_tools = registry.get_registered_tools()

# 创建FastAPI应用
app = FastAPI(
    title="Coli AI MCP Server",
    description="多MCP工具统一管理服务器，支持网页生成、天气查询、提示词管理等功能",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 动态挂载所有注册的MCP工具
for tool_name, mcp_instance in registered_tools.items():
    config = registry.tool_configs[tool_name]

    # 挂载Streamable HTTP端点
    http_endpoint = config["endpoints"]["http"]
    http_app = mcp_instance.streamable_http_app()
    app.mount(http_endpoint.removesuffix('/mcp'), http_app)

    # 挂载SSE端点
    sse_endpoint = config["endpoints"]["sse"]
    sse_app = mcp_instance.sse_app()
    sse_mount_path = sse_endpoint.removesuffix('/sse')
    app.mount(sse_mount_path, sse_app)

    print(f"  📡 {config['display_name']}: {http_endpoint} | {sse_endpoint}")

# 静态文件服务配置  
DOMAIN_MAPPINGS_FILE = os.path.join("webpage-generator", "domain_mappings.json")
DEFAULT_DOMAIN = "xiaohai-test.coli688.com"

def load_domain_mappings() -> dict:
    """加载域名映射"""
    if os.path.exists(DOMAIN_MAPPINGS_FILE):
        try:
            with open(DOMAIN_MAPPINGS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return {}
    return {}

def extract_subdomain(host: str) -> str:
    """从Host头中提取子域名"""
    if not host:
        return ""

    # 移除端口号
    host = host.split(':')[0]

    # 获取配置的域名，也移除端口号
    domain = os.getenv("DOMAIN", DEFAULT_DOMAIN).split(':')[0]

    # 检查是否是子域名
    if host.endswith(f".{domain}"):
        subdomain = host[:-len(f".{domain}")]
        return subdomain

    return ""

@app.get("/")
async def handle_root_request(request: Request):
    """处理根路径请求，支持子域名访问静态文件"""
    host = request.headers.get("host", "")
    subdomain = extract_subdomain(host)

    # 调试信息
    print(f"🔍 调试信息: host='{host}', subdomain='{subdomain}'")

    if subdomain:
        # 如果是子域名访问，提供静态文件服务
        try:
            # 加载域名映射
            mappings = load_domain_mappings()

            if subdomain not in mappings:
                raise HTTPException(status_code=404, detail=f"未找到子域名 '{subdomain}' 对应的文件")

            # 获取文件路径
            file_info = mappings[subdomain]
            file_path = file_info.get("file_path")

            if not file_path or not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail=f"文件不存在: {file_path}")

            # 返回HTML文件
            return FileResponse(file_path, media_type="text/html")

        except HTTPException:
            raise
        except Exception as e:
            return HTMLResponse(f"<h1>错误</h1><p>无法加载静态文件: {str(e)}</p>", status_code=500)

    # 如果不是子域名访问，返回正常的API信息
    return {
        "message": "Coli AI MCP Server",
        "version": "1.0.0",
        "registered_tools": len(registered_tools),
        "available_tools": registry.get_tool_list(),
        "client_config_examples": registry.get_client_configs()
    }

# 创建组合生命周期来管理所有会话管理器
@contextlib.asynccontextmanager
async def lifespan(_: FastAPI):
    """管理所有MCP服务器的生命周期"""
    async with contextlib.AsyncExitStack() as stack:
        # 动态启动所有MCP服务器的会话管理器
        for _, mcp_instance in registered_tools.items():
            await stack.enter_async_context(mcp_instance.session_manager.run())
        yield

# 设置生命周期
app.router.lifespan_context = lifespan





@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "message": "All MCP tools are running"}


def main():
    """启动服务器"""
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    main()
